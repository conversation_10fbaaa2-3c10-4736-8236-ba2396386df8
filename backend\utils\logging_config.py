# utils/logging_config.py
"""
Comprehensive logging configuration for the Coffee Meetings Platform.

This module provides structured logging with different levels, formatters,
and handlers for various components of the application.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Ensure logs directory exists
LOGS_DIR = BASE_DIR / 'logs'
LOGS_DIR.mkdir(exist_ok=True)

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'performance': {
            'format': '[PERF] {asctime} {name} {levelname} {message}',
            'style': '{',
        },
        'security': {
            'format': '[SECURITY] {asctime} {name} {levelname} {message}',
            'style': '{',
        },
        'audit': {
            'format': '[AUDIT] {asctime} {name} {levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
            'level': 'INFO',
        },
        'console_debug': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'level': 'DEBUG',
        },
        'file_general': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'general.log',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
            'level': 'INFO',
        },
        'file_error': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'error.log',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
            'level': 'ERROR',
        },
        'file_performance': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'performance.log',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
            'formatter': 'performance',
            'level': 'INFO',
        },
        'file_security': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'security.log',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
            'formatter': 'security',
            'level': 'WARNING',
        },
        'file_audit': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'audit.log',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 10,  # Keep more audit logs
            'formatter': 'audit',
            'level': 'INFO',
        },
        'file_api': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'api.log',
            'maxBytes': 10 * 1024 * 1024,  # 10MB
            'backupCount': 5,
            'formatter': 'json',
            'level': 'INFO',
        },
    },
    'loggers': {
        # Django core loggers
        'django': {
            'handlers': ['console', 'file_general'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console', 'file_error', 'file_api'],
            'level': 'WARNING',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['file_performance'],
            'level': 'WARNING',  # Only log slow queries and errors
            'propagate': False,
        },
        'django.security': {
            'handlers': ['console', 'file_security'],
            'level': 'WARNING',
            'propagate': False,
        },
        
        # Application loggers
        'users': {
            'handlers': ['console', 'file_general', 'file_audit'],
            'level': 'INFO',
            'propagate': False,
        },
        'users.authentication': {
            'handlers': ['console', 'file_security', 'file_audit'],
            'level': 'INFO',
            'propagate': False,
        },
        'campaigns': {
            'handlers': ['console', 'file_general', 'file_audit'],
            'level': 'INFO',
            'propagate': False,
        },
        'campaigns.services': {
            'handlers': ['console', 'file_general', 'file_audit'],
            'level': 'INFO',
            'propagate': False,
        },
        'employees': {
            'handlers': ['console', 'file_general'],
            'level': 'INFO',
            'propagate': False,
        },
        'matching': {
            'handlers': ['console', 'file_general', 'file_performance'],
            'level': 'INFO',
            'propagate': False,
        },
        'matching.services': {
            'handlers': ['console', 'file_performance', 'file_audit'],
            'level': 'INFO',
            'propagate': False,
        },
        'evaluations': {
            'handlers': ['console', 'file_general'],
            'level': 'INFO',
            'propagate': False,
        },
        'notifications': {
            'handlers': ['console', 'file_general'],
            'level': 'INFO',
            'propagate': False,
        },
        'dashboard': {
            'handlers': ['console', 'file_general', 'file_performance'],
            'level': 'INFO',
            'propagate': False,
        },
        
        # Utility loggers
        'utils.cache_utils': {
            'handlers': ['console', 'file_performance'],
            'level': 'INFO',
            'propagate': False,
        },
        'utils.services': {
            'handlers': ['console', 'file_general', 'file_audit'],
            'level': 'INFO',
            'propagate': False,
        },
        'utils.exceptions': {
            'handlers': ['console', 'file_error'],
            'level': 'WARNING',
            'propagate': False,
        },
        
        # Third-party loggers
        'axes': {
            'handlers': ['console', 'file_security'],
            'level': 'WARNING',
            'propagate': False,
        },
        'corsheaders': {
            'handlers': ['file_general'],
            'level': 'WARNING',
            'propagate': False,
        },
    },
    'root': {
        'handlers': ['console', 'file_general', 'file_error'],
        'level': 'INFO',
    },
}


def get_logging_config(debug=False):
    """
    Get logging configuration based on environment.
    
    Args:
        debug (bool): Whether to enable debug logging
        
    Returns:
        dict: Logging configuration
    """
    config = LOGGING_CONFIG.copy()
    
    if debug:
        # Add debug handlers and lower log levels for development
        config['handlers']['console']['level'] = 'DEBUG'
        config['loggers']['django.db.backends']['level'] = 'DEBUG'
        
        # Add debug console handler to all loggers
        for logger_name in config['loggers']:
            if 'handlers' in config['loggers'][logger_name]:
                if 'console_debug' not in config['loggers'][logger_name]['handlers']:
                    config['loggers'][logger_name]['handlers'].append('console_debug')
    
    return config


class StructuredLogger:
    """
    Utility class for structured logging with consistent formatting.
    """
    
    def __init__(self, name):
        self.logger = logging.getLogger(name)
    
    def log_user_action(self, user_id, action, resource_type=None, resource_id=None, details=None):
        """Log user actions for audit trail"""
        log_data = {
            'user_id': user_id,
            'action': action,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'details': details or {},
            'timestamp': timezone.now().isoformat()
        }
        self.logger.info(f"User action: {log_data}")
    
    def log_performance(self, operation, duration, details=None):
        """Log performance metrics"""
        log_data = {
            'operation': operation,
            'duration_ms': duration * 1000,  # Convert to milliseconds
            'details': details or {}
        }
        self.logger.info(f"Performance: {log_data}")
    
    def log_security_event(self, event_type, user_id=None, ip_address=None, details=None):
        """Log security events"""
        log_data = {
            'event_type': event_type,
            'user_id': user_id,
            'ip_address': ip_address,
            'details': details or {},
            'timestamp': timezone.now().isoformat()
        }
        self.logger.warning(f"Security event: {log_data}")
    
    def log_api_request(self, method, path, user_id=None, status_code=None, duration=None):
        """Log API requests"""
        log_data = {
            'method': method,
            'path': path,
            'user_id': user_id,
            'status_code': status_code,
            'duration_ms': duration * 1000 if duration else None,
            'timestamp': timezone.now().isoformat()
        }
        self.logger.info(f"API request: {log_data}")


# Import timezone
from django.utils import timezone
import logging
