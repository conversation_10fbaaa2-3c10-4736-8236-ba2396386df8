# campaigns/urls.py
from django.urls import path
from rest_framework.routers import <PERSON><PERSON>ult<PERSON>out<PERSON>
from .views import (
    CampaignViewSet,
    CampaignWorkflowStatusView,
    CampaignBulkWorkflowStatusView,
    CampaignWorkflowStepUpdateView,
    # Removed unused imports:
    # CampaignWorkflowValidationView,
    # CampaignWorkflowResetView,
)

router = DefaultRouter()
router.register(r'', CampaignViewSet, basename='campaign')

# Workflow URLs (optimized for performance)
workflow_urlpatterns = [
    # Bulk workflow status endpoint (NEW - for performance optimization)
    path('bulk-workflow-status/', CampaignBulkWorkflowStatusView.as_view(), name='campaign-bulk-workflow-status'),

    # Individual workflow endpoints
    path('<int:campaign_id>/workflow-status/', CampaignWorkflowStatusView.as_view(), name='campaign-workflow-status'),
    path('<int:campaign_id>/workflow-step/', CampaignWorkflowStepUpdateView.as_view(), name='campaign-workflow-step'),

    # REMOVED: workflow-validate and workflow-reset - Not used by frontend
    # path('<int:campaign_id>/workflow-validate/<int:step>/', CampaignWorkflowValidationView.as_view(), name='campaign-workflow-validate'),
    # path('<int:campaign_id>/workflow-reset/', CampaignWorkflowResetView.as_view(), name='campaign-workflow-reset'),
]

urlpatterns = [
    *workflow_urlpatterns,  # Unpack workflow patterns
    *router.urls  # Unpack router patterns
]
