# Coffee Meetings Platform - Coding Standards

## Overview

This document outlines the coding standards and best practices for the Coffee Meetings Platform. Following these standards ensures code consistency, maintainability, and quality across the entire codebase.

## General Principles

### 1. Code Quality
- **Readability**: Code should be self-documenting and easy to understand
- **Consistency**: Follow established patterns and conventions
- **Simplicity**: Prefer simple, clear solutions over complex ones
- **DRY (Don't Repeat Yourself)**: Avoid code duplication
- **SOLID Principles**: Follow object-oriented design principles

### 2. Documentation
- **Code Comments**: Explain why, not what
- **API Documentation**: Document all public APIs
- **README Files**: Provide clear setup and usage instructions
- **Architecture Documentation**: Maintain high-level system documentation

### 3. Testing
- **Test Coverage**: Aim for 80%+ test coverage
- **Test Types**: Unit, integration, and end-to-end tests
- **Test Quality**: Tests should be reliable, fast, and maintainable

## Backend Standards (Django/Python)

### 1. Python Code Style

#### PEP 8 Compliance
```python
# Good: Clear, descriptive names
def calculate_employee_matching_score(employee1, employee2, criteria):
    """Calculate matching score between two employees based on criteria."""
    pass

# Bad: Unclear, abbreviated names
def calc_emp_score(e1, e2, c):
    pass
```

#### Import Organization
```python
# Standard library imports
import os
import json
from datetime import datetime, timedelta

# Third-party imports
from django.db import models
from rest_framework import serializers

# Local application imports
from .models import Campaign
from utils.exceptions import ValidationException
```

#### Function and Class Documentation
```python
class CampaignService:
    """
    Service class for campaign management with comprehensive business logic.
    
    This service handles all campaign-related operations including creation,
    validation, workflow management, and statistics calculation.
    """
    
    def create_campaign(self, data: Dict[str, Any]) -> Campaign:
        """
        Create a new campaign with validation and workflow initialization.
        
        Args:
            data: Campaign data dictionary containing title, dates, etc.
            
        Returns:
            Campaign: The created campaign instance
            
        Raises:
            ValidationException: If campaign data is invalid
            PermissionDeniedException: If user lacks permissions
        """
        pass
```

### 2. Django Specific Standards

#### Model Design
```python
class Campaign(models.Model):
    """Campaign model with comprehensive field definitions and constraints."""
    
    title = models.CharField(
        max_length=100,
        help_text="Campaign title (max 100 characters)"
    )
    start_date = models.DateField(
        help_text="Campaign start date"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Timestamp when campaign was created"
    )
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['hr_manager', 'created_at']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(end_date__gt=models.F('start_date')),
                name='end_date_after_start_date'
            )
        ]
    
    def __str__(self):
        return f"{self.title} ({self.hr_manager.name})"
```

#### View Organization
```python
class CampaignViewSet(viewsets.ModelViewSet, StandardResponseMixin):
    """
    ViewSet for campaign CRUD operations with standardized responses.
    """
    
    serializer_class = CampaignSerializer
    permission_classes = [IsAuthenticated, IsCampaignOwner]
    
    def get_queryset(self):
        """Filter campaigns by authenticated user with optimizations."""
        return Campaign.objects.filter(hr_manager=self.request.user)\
                              .select_related('hr_manager')\
                              .prefetch_related('employee_set')
    
    def create(self, request, *args, **kwargs):
        """Create campaign with enhanced validation and logging."""
        try:
            service = CampaignService(user=request.user)
            campaign = service.create(request.data)
            serializer = self.get_serializer(campaign)
            return self.success_response(
                data=serializer.data,
                message="Campaign created successfully",
                status_code=status.HTTP_201_CREATED
            )
        except ValidationException as e:
            return self.error_response(
                message=e.message,
                code=e.code,
                details=e.details,
                status_code=status.HTTP_400_BAD_REQUEST
            )
```

#### Service Layer Pattern
```python
class CampaignService(CRUDService):
    """Campaign service with business logic encapsulation."""
    
    model_class = Campaign
    
    def validate_business_rules(self, data: Dict[str, Any], action: str = 'create'):
        """Validate campaign-specific business rules."""
        errors = {}
        
        # Date validation
        if data.get('start_date') and data.get('end_date'):
            if data['start_date'] >= data['end_date']:
                errors['end_date'] = 'End date must be after start date'
        
        if errors:
            raise ValidationException('Validation failed', details=errors)
```

### 3. Error Handling
```python
# Use custom exceptions for business logic errors
try:
    campaign = service.create(data)
except ValidationException as e:
    logger.warning(f"Validation error: {e.message}", extra={'details': e.details})
    return error_response(e.message, e.code, e.details)
except PermissionDeniedException as e:
    logger.warning(f"Permission denied: {e.message}")
    return error_response(e.message, e.code, status_code=403)
except Exception as e:
    logger.error(f"Unexpected error: {e}", exc_info=True)
    return error_response("An unexpected error occurred", "INTERNAL_ERROR")
```

## Frontend Standards (React/JavaScript)

### 1. JavaScript/React Code Style

#### Component Structure
```jsx
// components/campaigns/CampaignCard.jsx
/**
 * Campaign card component for displaying campaign summary information.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.campaign - Campaign data object
 * @param {Function} props.onEdit - Callback for edit action
 * @param {Function} props.onDelete - Callback for delete action
 */
const CampaignCard = ({ campaign, onEdit, onDelete }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { captureError } = useErrorHandler();
  
  const handleEdit = useCallback(async () => {
    try {
      setIsLoading(true);
      await onEdit(campaign.id);
    } catch (error) {
      captureError(error);
    } finally {
      setIsLoading(false);
    }
  }, [campaign.id, onEdit, captureError]);
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900">
        {campaign.title}
      </h3>
      {/* Component content */}
    </div>
  );
};

export default CampaignCard;
```

#### Custom Hooks
```jsx
// hooks/useCampaigns.js
/**
 * Custom hook for campaign data management with caching and error handling.
 * 
 * @param {Object} options - Hook options
 * @param {boolean} options.enabled - Whether to enable the query
 * @returns {Object} Query result with data, loading, error states
 */
export const useCampaigns = (options = {}) => {
  const { enabled = true } = options;
  
  return useQuery({
    queryKey: ['campaigns'],
    queryFn: () => campaignService.getCampaigns(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error.response?.status >= 400 && error.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
    onError: (error) => {
      console.error('Failed to fetch campaigns:', error);
    }
  });
};
```

#### Service Layer
```javascript
// services/campaignService.js
/**
 * Campaign service for API interactions with comprehensive error handling.
 */
export const campaignService = {
  /**
   * Get all campaigns with optional filtering.
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} API response with campaigns data
   */
  getCampaigns: async (params = {}) => {
    try {
      const response = await apiClient.get('/campaigns/', { params });
      return {
        success: true,
        data: response.data.results || response.data,
        pagination: response.data.pagination
      };
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      throw new Error(
        error.response?.data?.error?.message || 
        'Failed to fetch campaigns'
      );
    }
  }
};
```

### 2. Component Organization

#### File Structure
```
components/
├── campaigns/
│   ├── CampaignCard.jsx          # Individual campaign display
│   ├── CampaignForm.jsx          # Campaign creation/editing
│   ├── CampaignList.jsx          # Campaign listing
│   └── index.js                  # Export barrel
├── common/
│   ├── ErrorBoundary.jsx         # Error handling
│   ├── LoadingSpinner.jsx        # Loading states
│   └── ConfirmDialog.jsx         # Confirmation dialogs
└── ui/
    ├── Button.jsx                # Base button component
    ├── Input.jsx                 # Base input component
    └── Modal.jsx                 # Base modal component
```

#### Prop Types and Validation
```jsx
import PropTypes from 'prop-types';

const CampaignCard = ({ campaign, onEdit, onDelete }) => {
  // Component implementation
};

CampaignCard.propTypes = {
  campaign: PropTypes.shape({
    id: PropTypes.number.isRequired,
    title: PropTypes.string.isRequired,
    start_date: PropTypes.string.isRequired,
    end_date: PropTypes.string.isRequired
  }).isRequired,
  onEdit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired
};

CampaignCard.defaultProps = {
  onEdit: () => {},
  onDelete: () => {}
};
```

### 3. State Management

#### Context Usage
```jsx
// contexts/CampaignContext.jsx
const CampaignContext = createContext();

export const CampaignProvider = ({ children }) => {
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [filters, setFilters] = useState({});
  
  const value = useMemo(() => ({
    selectedCampaign,
    setSelectedCampaign,
    filters,
    setFilters
  }), [selectedCampaign, filters]);
  
  return (
    <CampaignContext.Provider value={value}>
      {children}
    </CampaignContext.Provider>
  );
};

export const useCampaignContext = () => {
  const context = useContext(CampaignContext);
  if (!context) {
    throw new Error('useCampaignContext must be used within CampaignProvider');
  }
  return context;
};
```

## Testing Standards

### 1. Backend Testing

#### Unit Tests
```python
# tests/test_campaign_service.py
class CampaignServiceTestCase(ServiceTestCase):
    """Comprehensive tests for CampaignService."""
    
    def setUp(self):
        super().setUp()
        self.service = CampaignService(user=self.hr_manager)
    
    def test_create_campaign_success(self):
        """Test successful campaign creation with workflow initialization."""
        data = {
            'title': 'Test Campaign',
            'start_date': date.today() + timedelta(days=1),
            'end_date': date.today() + timedelta(days=30)
        }
        
        campaign = self.service.create(data)
        
        self.assertIsNotNone(campaign)
        self.assertEqual(campaign.title, 'Test Campaign')
        self.assertTrue(hasattr(campaign, 'workflow_state'))
    
    def test_create_campaign_validation_error(self):
        """Test campaign creation with invalid data."""
        data = {
            'title': '',  # Invalid: empty title
            'start_date': date.today() + timedelta(days=30),
            'end_date': date.today() + timedelta(days=1)  # Invalid: end before start
        }
        
        with self.assertRaises(ValidationException) as context:
            self.service.create(data)
        
        self.assertIn('title', str(context.exception.details))
        self.assertIn('end_date', str(context.exception.details))
```

#### API Tests
```python
# tests/test_campaign_api.py
class CampaignAPITestCase(APIBaseTestCase):
    """Test campaign API endpoints."""
    
    def test_create_campaign_authenticated(self):
        """Test campaign creation with authentication."""
        self.authenticate()
        
        data = {
            'title': 'API Test Campaign',
            'start_date': '2024-12-01',
            'end_date': '2024-12-31'
        }
        
        response = self.client.post('/campaigns/', data)
        
        self.assert_api_success(response, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['title'], 'API Test Campaign')
    
    def test_create_campaign_unauthenticated(self):
        """Test campaign creation without authentication."""
        data = {'title': 'Test Campaign'}
        
        response = self.client.post('/campaigns/', data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
```

### 2. Frontend Testing

#### Component Tests
```jsx
// components/__tests__/CampaignCard.test.jsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import CampaignCard from '../CampaignCard';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('CampaignCard', () => {
  const mockCampaign = {
    id: 1,
    title: 'Test Campaign',
    start_date: '2024-12-01',
    end_date: '2024-12-31'
  };
  
  const mockOnEdit = jest.fn();
  const mockOnDelete = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders campaign information correctly', () => {
    render(
      <CampaignCard 
        campaign={mockCampaign}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />,
      { wrapper: createWrapper() }
    );
    
    expect(screen.getByText('Test Campaign')).toBeInTheDocument();
    expect(screen.getByText(/December 1, 2024/)).toBeInTheDocument();
  });
  
  it('calls onEdit when edit button is clicked', async () => {
    render(
      <CampaignCard 
        campaign={mockCampaign}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />,
      { wrapper: createWrapper() }
    );
    
    fireEvent.click(screen.getByRole('button', { name: /edit/i }));
    
    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith(1);
    });
  });
});
```

## Code Review Guidelines

### 1. Review Checklist

#### Functionality
- [ ] Code works as intended
- [ ] Edge cases are handled
- [ ] Error handling is comprehensive
- [ ] Performance considerations are addressed

#### Code Quality
- [ ] Code follows established patterns
- [ ] Functions are single-purpose and well-named
- [ ] Comments explain complex logic
- [ ] No code duplication

#### Testing
- [ ] Tests cover new functionality
- [ ] Tests are reliable and fast
- [ ] Test names are descriptive

#### Security
- [ ] Input validation is present
- [ ] Authentication/authorization is correct
- [ ] No sensitive data exposure

### 2. Review Process

1. **Self Review**: Author reviews their own code first
2. **Peer Review**: At least one team member reviews
3. **Testing**: All tests pass before merge
4. **Documentation**: Update relevant documentation

## Performance Guidelines

### 1. Backend Performance

#### Database Optimization
```python
# Good: Use select_related for foreign keys
campaigns = Campaign.objects.select_related('hr_manager')\
                           .prefetch_related('employee_set')

# Good: Use indexes for frequent queries
class Campaign(models.Model):
    class Meta:
        indexes = [
            models.Index(fields=['hr_manager', 'created_at']),
        ]

# Good: Use pagination for large datasets
class CampaignPagination(PageNumberPagination):
    page_size = 20
    max_page_size = 100
```

#### Caching Strategy
```python
# Good: Cache expensive operations
@cached_result(timeout=300, key_prefix='campaign_stats')
def get_campaign_statistics(campaign_id):
    # Expensive calculation
    pass

# Good: Invalidate cache when data changes
def update_campaign(campaign_id, data):
    campaign = Campaign.objects.get(id=campaign_id)
    # Update campaign
    CampaignCache.invalidate_campaign_cache(campaign_id)
```

### 2. Frontend Performance

#### Component Optimization
```jsx
// Good: Memoize expensive calculations
const CampaignList = ({ campaigns, filters }) => {
  const filteredCampaigns = useMemo(() => {
    return campaigns.filter(campaign => 
      campaign.title.toLowerCase().includes(filters.search.toLowerCase())
    );
  }, [campaigns, filters.search]);
  
  return (
    <div>
      {filteredCampaigns.map(campaign => (
        <CampaignCard key={campaign.id} campaign={campaign} />
      ))}
    </div>
  );
};

// Good: Use React.memo for pure components
const CampaignCard = React.memo(({ campaign }) => {
  return <div>{campaign.title}</div>;
});
```

#### Bundle Optimization
```jsx
// Good: Lazy load heavy components
const CampaignWorkflow = lazy(() => import('./CampaignWorkflow'));

// Good: Code splitting by route
const App = () => (
  <Routes>
    <Route path="/campaigns/:id/workflow" element={
      <Suspense fallback={<LoadingSpinner />}>
        <CampaignWorkflow />
      </Suspense>
    } />
  </Routes>
);
```

## Conclusion

These coding standards ensure consistency, maintainability, and quality across the Coffee Meetings Platform. All team members should follow these guidelines and participate in code reviews to maintain high standards.

For questions or suggestions about these standards, please create an issue in the project repository or discuss with the development team.
