[tool:pytest]
DJANGO_SETTINGS_MODULE = coffee_meetings_platform.test_settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test* *Tests
python_functions = test_*
testpaths = tests
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --reuse-db
    --nomigrations
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    slow: Slow tests
    performance: Performance tests
    security: Security tests
    smoke: Smoke tests
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
