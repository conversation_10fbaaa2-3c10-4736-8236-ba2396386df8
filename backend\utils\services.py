# utils/services.py
"""
Base service classes and utilities for the Coffee Meetings Platform.

This module provides standardized service layer patterns to ensure consistent
business logic implementation across the entire application.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from django.db import transaction, models
from django.core.exceptions import ValidationError
from django.utils import timezone
from .exceptions import (
    ValidationException, 
    BusinessLogicException, 
    ResourceNotFoundException,
    PermissionDeniedException
)
import logging

logger = logging.getLogger(__name__)


class BaseService(ABC):
    """
    Abstract base service class that provides common functionality
    for all service classes in the application.
    """
    
    def __init__(self, user=None):
        self.user = user
        self.logger = logging.getLogger(self.__class__.__module__)
    
    def log_action(self, action: str, details: Dict[str, Any] = None):
        """Log service actions for audit trail"""
        log_data = {
            'service': self.__class__.__name__,
            'action': action,
            'user': getattr(self.user, 'id', None),
            'timestamp': timezone.now().isoformat(),
            'details': details or {}
        }
        self.logger.info(f"Service action: {log_data}")
    
    def validate_permissions(self, action: str, resource: Any = None):
        """Validate user permissions for specific actions"""
        if not self.user:
            raise PermissionDeniedException(action, resource)
        
        # Override in subclasses for specific permission logic
        return True
    
    def validate_business_rules(self, data: Dict[str, Any], action: str = 'create'):
        """Validate business rules - override in subclasses"""
        pass


class CRUDService(BaseService):
    """
    Generic CRUD service that provides standard Create, Read, Update, Delete operations
    with proper error handling and validation.
    """
    
    model_class = None
    
    def __init__(self, user=None):
        super().__init__(user)
        if not self.model_class:
            raise ValueError("model_class must be defined in subclass")
    
    def get_queryset(self):
        """Get base queryset - override for filtering"""
        return self.model_class.objects.all()
    
    def get_by_id(self, obj_id: int) -> models.Model:
        """Get object by ID with error handling"""
        try:
            obj = self.get_queryset().get(id=obj_id)
            self.validate_permissions('read', obj)
            return obj
        except self.model_class.DoesNotExist:
            raise ResourceNotFoundException(self.model_class.__name__, obj_id)
    
    def list(self, filters: Dict[str, Any] = None, ordering: str = None) -> List[models.Model]:
        """List objects with optional filtering and ordering"""
        self.validate_permissions('list')
        
        queryset = self.get_queryset()
        
        if filters:
            queryset = queryset.filter(**filters)
        
        if ordering:
            queryset = queryset.order_by(ordering)
        
        return list(queryset)
    
    @transaction.atomic
    def create(self, data: Dict[str, Any]) -> models.Model:
        """Create new object with validation"""
        self.validate_permissions('create')
        self.validate_business_rules(data, 'create')
        
        try:
            obj = self.model_class.objects.create(**data)
            self.log_action('create', {'object_id': obj.id, 'data': data})
            return obj
        except ValidationError as e:
            raise ValidationException(str(e))
    
    @transaction.atomic
    def update(self, obj_id: int, data: Dict[str, Any]) -> models.Model:
        """Update existing object with validation"""
        obj = self.get_by_id(obj_id)
        self.validate_permissions('update', obj)
        self.validate_business_rules(data, 'update')
        
        try:
            for key, value in data.items():
                setattr(obj, key, value)
            obj.full_clean()
            obj.save()
            
            self.log_action('update', {'object_id': obj.id, 'data': data})
            return obj
        except ValidationError as e:
            raise ValidationException(str(e))
    
    @transaction.atomic
    def delete(self, obj_id: int) -> bool:
        """Delete object with validation"""
        obj = self.get_by_id(obj_id)
        self.validate_permissions('delete', obj)
        
        self.log_action('delete', {'object_id': obj.id})
        obj.delete()
        return True


class WorkflowService(BaseService):
    """
    Base service for workflow management with state transitions
    """
    
    def __init__(self, user=None):
        super().__init__(user)
        self.workflow_states = {}  # Override in subclasses
    
    def validate_transition(self, from_state: str, to_state: str) -> bool:
        """Validate if state transition is allowed"""
        allowed_transitions = self.workflow_states.get(from_state, [])
        if to_state not in allowed_transitions:
            raise BusinessLogicException(
                f"Invalid state transition from {from_state} to {to_state}",
                {'from_state': from_state, 'to_state': to_state, 'allowed': allowed_transitions}
            )
        return True
    
    @transaction.atomic
    def transition_state(self, obj: models.Model, new_state: str, data: Dict[str, Any] = None):
        """Perform state transition with validation"""
        current_state = getattr(obj, 'state', None)
        
        if current_state:
            self.validate_transition(current_state, new_state)
        
        # Perform pre-transition actions
        self.pre_transition_hook(obj, current_state, new_state, data)
        
        # Update state
        obj.state = new_state
        if data:
            for key, value in data.items():
                if hasattr(obj, key):
                    setattr(obj, key, value)
        
        obj.save()
        
        # Perform post-transition actions
        self.post_transition_hook(obj, current_state, new_state, data)
        
        self.log_action('state_transition', {
            'object_id': obj.id,
            'from_state': current_state,
            'to_state': new_state,
            'data': data
        })
        
        return obj
    
    def pre_transition_hook(self, obj: models.Model, from_state: str, to_state: str, data: Dict[str, Any]):
        """Override in subclasses for pre-transition logic"""
        pass
    
    def post_transition_hook(self, obj: models.Model, from_state: str, to_state: str, data: Dict[str, Any]):
        """Override in subclasses for post-transition logic"""
        pass


class EmailService(BaseService):
    """
    Service for handling email operations with proper error handling
    """
    
    def __init__(self, user=None):
        super().__init__(user)
        self.email_backend = self._get_email_backend()
    
    def _get_email_backend(self):
        """Get configured email backend"""
        from django.core.mail import get_connection
        return get_connection()
    
    def send_email(self, 
                   subject: str, 
                   message: str, 
                   recipient_list: List[str], 
                   from_email: str = None,
                   html_message: str = None) -> bool:
        """Send email with error handling and logging"""
        try:
            from django.core.mail import send_mail
            
            result = send_mail(
                subject=subject,
                message=message,
                from_email=from_email,
                recipient_list=recipient_list,
                html_message=html_message,
                fail_silently=False
            )
            
            self.log_action('send_email', {
                'subject': subject,
                'recipients': recipient_list,
                'success': bool(result)
            })
            
            return bool(result)
            
        except Exception as e:
            self.logger.error(f"Failed to send email: {e}", exc_info=True)
            self.log_action('send_email_failed', {
                'subject': subject,
                'recipients': recipient_list,
                'error': str(e)
            })
            return False
    
    def send_bulk_email(self, email_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """Send multiple emails with batch processing"""
        results = {'sent': 0, 'failed': 0}
        
        for email in email_data:
            success = self.send_email(**email)
            if success:
                results['sent'] += 1
            else:
                results['failed'] += 1
        
        self.log_action('send_bulk_email', results)
        return results


class CacheService(BaseService):
    """
    Service for cache operations with consistent patterns
    """
    
    def __init__(self, user=None):
        super().__init__(user)
        from django.core.cache import cache
        self.cache = cache
    
    def get_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate consistent cache keys"""
        import hashlib
        import json
        
        key_data = {
            'prefix': prefix,
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
    
    def get_or_set(self, key: str, callable_func, timeout: int = 300):
        """Get from cache or set if not exists"""
        value = self.cache.get(key)
        if value is None:
            value = callable_func()
            self.cache.set(key, value, timeout)
            self.log_action('cache_set', {'key': key, 'timeout': timeout})
        else:
            self.log_action('cache_hit', {'key': key})
        return value
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate cache keys matching pattern"""
        # Implementation depends on cache backend
        self.cache.clear()  # Simplified for now
        self.log_action('cache_invalidate', {'pattern': pattern})
