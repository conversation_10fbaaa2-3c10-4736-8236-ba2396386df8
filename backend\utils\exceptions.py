# utils/exceptions.py
"""
Custom exception classes and error handling utilities for the Coffee Meetings Platform.

This module provides standardized exception handling and error response formatting
to ensure consistent error handling across the entire application.
"""

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import exception_handler
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import IntegrityError
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class CoffeeMeetingsException(Exception):
    """Base exception class for Coffee Meetings Platform"""
    
    def __init__(self, message, code=None, details=None):
        self.message = message
        self.code = code or 'GENERAL_ERROR'
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(CoffeeMeetingsException):
    """Exception for validation errors"""
    
    def __init__(self, message, field=None, details=None):
        self.field = field
        super().__init__(message, 'VALIDATION_ERROR', details)


class BusinessLogicException(CoffeeMeetingsException):
    """Exception for business logic violations"""
    
    def __init__(self, message, details=None):
        super().__init__(message, 'BUSINESS_LOGIC_ERROR', details)


class ResourceNotFoundException(CoffeeMeetingsException):
    """Exception for resource not found errors"""
    
    def __init__(self, resource_type, resource_id=None):
        message = f"{resource_type} not found"
        if resource_id:
            message += f" with ID: {resource_id}"
        super().__init__(message, 'RESOURCE_NOT_FOUND', {'resource_type': resource_type, 'resource_id': resource_id})


class PermissionDeniedException(CoffeeMeetingsException):
    """Exception for permission denied errors"""
    
    def __init__(self, action, resource=None):
        message = f"Permission denied for action: {action}"
        if resource:
            message += f" on resource: {resource}"
        super().__init__(message, 'PERMISSION_DENIED', {'action': action, 'resource': resource})


class ServiceUnavailableException(CoffeeMeetingsException):
    """Exception for service unavailable errors"""
    
    def __init__(self, service_name, details=None):
        message = f"Service unavailable: {service_name}"
        super().__init__(message, 'SERVICE_UNAVAILABLE', details)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error response format.
    
    Returns:
        Response: Standardized error response
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Log the exception
    logger.error(f"Exception in {context.get('view', 'Unknown view')}: {exc}", exc_info=True)
    
    if response is not None:
        # Handle DRF exceptions
        custom_response_data = {
            'success': False,
            'error': {
                'code': getattr(exc, 'default_code', 'UNKNOWN_ERROR'),
                'message': str(exc),
                'details': response.data if isinstance(response.data, dict) else {'detail': response.data}
            },
            'timestamp': timezone.now().isoformat(),
            'path': context.get('request').path if context.get('request') else None
        }
        response.data = custom_response_data
        return response
    
    # Handle custom exceptions
    if isinstance(exc, CoffeeMeetingsException):
        return Response({
            'success': False,
            'error': {
                'code': exc.code,
                'message': exc.message,
                'details': exc.details
            },
            'timestamp': timezone.now().isoformat(),
            'path': context.get('request').path if context.get('request') else None
        }, status=_get_status_code_for_exception(exc))
    
    # Handle Django validation errors
    if isinstance(exc, DjangoValidationError):
        return Response({
            'success': False,
            'error': {
                'code': 'VALIDATION_ERROR',
                'message': 'Validation failed',
                'details': {'validation_errors': exc.messages}
            },
            'timestamp': timezone.now().isoformat(),
            'path': context.get('request').path if context.get('request') else None
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Handle database integrity errors
    if isinstance(exc, IntegrityError):
        return Response({
            'success': False,
            'error': {
                'code': 'INTEGRITY_ERROR',
                'message': 'Database integrity constraint violation',
                'details': {'database_error': str(exc)}
            },
            'timestamp': timezone.now().isoformat(),
            'path': context.get('request').path if context.get('request') else None
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Handle unexpected exceptions
    logger.critical(f"Unhandled exception: {exc}", exc_info=True)
    return Response({
        'success': False,
        'error': {
            'code': 'INTERNAL_SERVER_ERROR',
            'message': 'An unexpected error occurred',
            'details': {}
        },
        'timestamp': timezone.now().isoformat(),
        'path': context.get('request').path if context.get('request') else None
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _get_status_code_for_exception(exc):
    """Get appropriate HTTP status code for custom exceptions"""
    status_map = {
        'VALIDATION_ERROR': status.HTTP_400_BAD_REQUEST,
        'BUSINESS_LOGIC_ERROR': status.HTTP_422_UNPROCESSABLE_ENTITY,
        'RESOURCE_NOT_FOUND': status.HTTP_404_NOT_FOUND,
        'PERMISSION_DENIED': status.HTTP_403_FORBIDDEN,
        'SERVICE_UNAVAILABLE': status.HTTP_503_SERVICE_UNAVAILABLE,
    }
    return status_map.get(exc.code, status.HTTP_500_INTERNAL_SERVER_ERROR)


class StandardResponseMixin:
    """
    Mixin to provide standardized response formatting for API views.
    """
    
    def success_response(self, data=None, message=None, status_code=status.HTTP_200_OK):
        """Create a standardized success response"""
        response_data = {
            'success': True,
            'data': data,
            'message': message,
            'timestamp': timezone.now().isoformat()
        }
        return Response(response_data, status=status_code)
    
    def error_response(self, message, code='ERROR', details=None, status_code=status.HTTP_400_BAD_REQUEST):
        """Create a standardized error response"""
        response_data = {
            'success': False,
            'error': {
                'code': code,
                'message': message,
                'details': details or {}
            },
            'timestamp': timezone.now().isoformat()
        }
        return Response(response_data, status=status_code)
    
    def paginated_response(self, queryset, serializer_class, request, message=None):
        """Create a standardized paginated response"""
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = serializer_class(page, many=True, context={'request': request})
            paginated_data = self.get_paginated_response(serializer.data)
            return self.success_response(
                data=paginated_data.data,
                message=message or f"Retrieved {len(serializer.data)} items"
            )
        
        serializer = serializer_class(queryset, many=True, context={'request': request})
        return self.success_response(
            data=serializer.data,
            message=message or f"Retrieved {len(serializer.data)} items"
        )
