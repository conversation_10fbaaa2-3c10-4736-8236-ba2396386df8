from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import EmployeeViewSet
# Removed unused import: EmployeeAttributeViewSet

# Router for employees - using limited actions only
employee_router = DefaultRouter()
employee_router.register(r'', EmployeeViewSet, basename='employee')

urlpatterns = [
    # REMOVED: Employee attributes endpoints - Not used by frontend
    # path('attributes/', EmployeeAttributeViewSet.as_view({'get': 'list', 'post': 'create'}), name='employeeattribute-list'),
    # path('attributes/<int:pk>/', EmployeeAttributeViewSet.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}), name='employeeattribute-detail'),

    # Employee routes (limited to used endpoints only)
    path('', include(employee_router.urls)),
]

# ACTIVE ENDPOINTS (Used by frontend):
# GET /employees/ - List all employees (with filtering by campaign)
# POST /employees/ - Create a new employee
# DELETE /employees/{id}/ - Delete specific employee
# POST /employees/upload-excel/ - Upload and process Excel file
#
# REMOVED ENDPOINTS (Not used by frontend):
# GET /employees/{id}/ - Individual employee details
# PUT/PATCH /employees/{id}/ - Employee update operations
# GET /employees/by-campaign/ - Alternative campaign filtering
# DELETE /employees/delete-by-campaign/ - Bulk delete operations
# All /employees/attributes/ endpoints - Attribute management system
