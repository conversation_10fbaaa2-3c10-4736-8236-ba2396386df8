from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
# Removed unused import: MultiPartParser, FormParser
from .serializers import (
    HRManagerLoginSerializer,
    HRManagerRegisterSerializer,
    HRManagerProfileSerializer,
    # Removed unused serializers:
    # ProfilePictureUploadSerializer,
    # PasswordResetRequestSerializer,
    # PasswordResetConfirmSerializer,
    # ChangePasswordSerializer
)
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.decorators import api_view, permission_classes
from rest_framework.generics import RetrieveUpdateAPIView
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .authentication import CustomJWTAuthentication
from .models import HRManager
# Removed unused import: PasswordResetToken
@method_decorator(csrf_exempt, name='dispatch')
class HRManagerLoginView(APIView):
    permission_classes = [AllowAny]  # Pas d'authentification requise pour login

    def post(self, request):
        serializer = HRManagerLoginSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            return Response(serializer.validated_data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='dispatch')
class HRManagerRegisterView(APIView):
    permission_classes = [AllowAny]  # Pas d'authentification requise pour register

    def post(self, request):
        serializer = HRManagerRegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    
class HRManagerProfileView(RetrieveUpdateAPIView):
    serializer_class = HRManagerProfileSerializer
    authentication_classes = [CustomJWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get_object(self):
        # Return the authenticated HRManager instance
        return self.request.user

    def get_serializer_context(self):
        """Add request to serializer context for URL building"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context




# REMOVED: PasswordResetRequestView - Not used by frontend


# REMOVED: PasswordResetConfirmView - Not used by frontend


# REMOVED: ChangePasswordView - Not used by frontend


# REMOVED: ProfilePictureUploadView - Not used by frontend