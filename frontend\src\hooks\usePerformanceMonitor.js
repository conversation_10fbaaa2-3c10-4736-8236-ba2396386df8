import React, { useEffect, useRef, useCallback } from 'react';

/**
 * Performance monitoring hook for tracking API requests and render times
 */
export const usePerformanceMonitor = (componentName = 'Component') => {
  const metricsRef = useRef({
    apiRequests: [],
    renderTimes: [],
    componentName
  });

  // Track API request performance
  const trackApiRequest = useCallback((endpoint, startTime, endTime, success = true) => {
    const duration = endTime - startTime;
    const metric = {
      endpoint,
      duration,
      success,
      timestamp: Date.now()
    };
    
    metricsRef.current.apiRequests.push(metric);
    
    // Keep only last 50 requests to prevent memory leaks
    if (metricsRef.current.apiRequests.length > 50) {
      metricsRef.current.apiRequests = metricsRef.current.apiRequests.slice(-50);
    }
    
    // Log slow requests in development
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`🐌 Slow API request detected:`, {
        endpoint,
        duration: `${duration}ms`,
        component: componentName
      });
    }
    
    return metric;
  }, [componentName]);

  // Track render performance
  const trackRender = useCallback((renderTime) => {
    const metric = {
      renderTime,
      timestamp: Date.now()
    };
    
    metricsRef.current.renderTimes.push(metric);
    
    // Keep only last 20 render times
    if (metricsRef.current.renderTimes.length > 20) {
      metricsRef.current.renderTimes = metricsRef.current.renderTimes.slice(-20);
    }
    
    // Log slow renders in development
    if (process.env.NODE_ENV === 'development' && renderTime > 100) {
      console.warn(`🐌 Slow render detected:`, {
        renderTime: `${renderTime}ms`,
        component: componentName
      });
    }
    
    return metric;
  }, [componentName]);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const apiRequests = metricsRef.current.apiRequests;
    const renderTimes = metricsRef.current.renderTimes;
    
    const apiSummary = apiRequests.length > 0 ? {
      totalRequests: apiRequests.length,
      averageDuration: apiRequests.reduce((sum, req) => sum + req.duration, 0) / apiRequests.length,
      slowestRequest: Math.max(...apiRequests.map(req => req.duration)),
      fastestRequest: Math.min(...apiRequests.map(req => req.duration)),
      successRate: (apiRequests.filter(req => req.success).length / apiRequests.length) * 100
    } : null;
    
    const renderSummary = renderTimes.length > 0 ? {
      totalRenders: renderTimes.length,
      averageRenderTime: renderTimes.reduce((sum, render) => sum + render.renderTime, 0) / renderTimes.length,
      slowestRender: Math.max(...renderTimes.map(render => render.renderTime)),
      fastestRender: Math.min(...renderTimes.map(render => render.renderTime))
    } : null;
    
    return {
      component: componentName,
      api: apiSummary,
      render: renderSummary,
      timestamp: Date.now()
    };
  }, [componentName]);

  // Log performance summary on unmount (development only)
  useEffect(() => {
    return () => {
      if (process.env.NODE_ENV === 'development') {
        const summary = getPerformanceSummary();
        if (summary.api || summary.render) {
          console.log(`📊 Performance Summary for ${componentName}:`, summary);
        }
      }
    };
  }, [componentName, getPerformanceSummary]);

  return {
    trackApiRequest,
    trackRender,
    getPerformanceSummary
  };
};

/**
 * Higher-order component for automatic performance tracking
 */
export const withPerformanceMonitoring = (WrappedComponent, componentName) => {
  return React.memo((props) => {
    const renderStartTime = useRef(performance.now());
    const { trackRender } = usePerformanceMonitor(componentName);
    
    useEffect(() => {
      const renderEndTime = performance.now();
      const renderDuration = renderEndTime - renderStartTime.current;
      trackRender(renderDuration);
      renderStartTime.current = renderEndTime;
    });
    
    return <WrappedComponent {...props} />;
  });
};

/**
 * Hook for tracking API request batches
 */
export const useApiRequestBatcher = (batchDelay = 100) => {
  const batchRef = useRef([]);
  const timeoutRef = useRef(null);
  
  const addToBatch = useCallback((request) => {
    batchRef.current.push(request);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      const batch = [...batchRef.current];
      batchRef.current = [];
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`📦 Processing batch of ${batch.length} requests`);
      }
      
      // Process batch here
      batch.forEach(request => request());
    }, batchDelay);
  }, [batchDelay]);
  
  const clearBatch = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    batchRef.current = [];
  }, []);
  
  useEffect(() => {
    return () => clearBatch();
  }, [clearBatch]);
  
  return { addToBatch, clearBatch };
};

export default usePerformanceMonitor;
