# Generated migration for performance optimization

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0007_add_workflow_performance_indexes'),
    ]

    operations = [
        # Add composite indexes for common query patterns
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS campaigns_hr_manager_created_at_idx ON campaigns_campaign (hr_manager_id, created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS campaigns_hr_manager_created_at_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS campaigns_hr_manager_end_date_idx ON campaigns_campaign (hr_manager_id, end_date);",
            reverse_sql="DROP INDEX IF EXISTS campaigns_hr_manager_end_date_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS campaigns_status_lookup_idx ON campaigns_campaign (hr_manager_id, end_date, created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS campaigns_status_lookup_idx;"
        ),
        
        # Add indexes for workflow state queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS workflow_completed_steps_gin_idx ON campaign_workflow_state USING GIN (completed_steps);",
            reverse_sql="DROP INDEX IF EXISTS workflow_completed_steps_gin_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS workflow_campaign_completed_idx ON campaign_workflow_state (campaign_id, completed_steps);",
            reverse_sql="DROP INDEX IF EXISTS workflow_campaign_completed_idx;"
        ),
        
        # Add indexes for employee and matching criteria queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS employees_campaign_name_idx ON employees_employee (campaign_id, name);",
            reverse_sql="DROP INDEX IF EXISTS employees_campaign_name_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS matching_criteria_campaign_idx ON matching_campaignmatchingcriteria (campaign_id, created_at);",
            reverse_sql="DROP INDEX IF EXISTS matching_criteria_campaign_idx;"
        ),
    ]
