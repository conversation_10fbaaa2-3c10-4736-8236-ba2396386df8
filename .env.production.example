# .env.production.example
# Production environment configuration template for Coffee Meetings Platform
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# GENERAL SETTINGS
# =============================================================================

# Environment
ENVIRONMENT=production
DEBUG=False

# Domain and URLs
DOMAIN=your-domain.com
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Django Secret Key (generate with: python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())')
SECRET_KEY=your-super-secret-django-key-here

# JWT Secret Key (generate with: openssl rand -base64 32)
JWT_SECRET_KEY=your-jwt-secret-key-here

# SSL/TLS Settings
USE_TLS=True
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# PostgreSQL Configuration
POSTGRES_DB=coffee_meetings_prod
POSTGRES_USER=coffee_user_prod
POSTGRES_PASSWORD=your-secure-database-password-here
POSTGRES_HOST=db
POSTGRES_PORT=5432

# Database URL (alternative to individual settings)
DATABASE_URL=************************************************************************/coffee_meetings_prod

# =============================================================================
# REDIS SETTINGS
# =============================================================================

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password-here
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=1

# Redis URL (alternative to individual settings)
REDIS_URL=redis://:your-secure-redis-password-here@redis:6379/1

# =============================================================================
# EMAIL SETTINGS
# =============================================================================

# SMTP Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password-here
DEFAULT_FROM_EMAIL=<EMAIL>

# Email Templates
SUPPORT_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# FILE STORAGE SETTINGS
# =============================================================================

# Static and Media Files
STATIC_URL=/static/
MEDIA_URL=/media/
STATIC_ROOT=/app/staticfiles
MEDIA_ROOT=/app/media

# AWS S3 Configuration (if using S3 for file storage)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
# AWS_S3_REGION_NAME=us-east-1
# AWS_S3_CUSTOM_DOMAIN=your-cloudfront-domain.com
# USE_S3=True

# =============================================================================
# DOCKER SETTINGS
# =============================================================================

# Docker Registry
DOCKER_REGISTRY=your-registry.com/
IMAGE_TAG=latest

# Container Resources
POSTGRES_MAX_CONNECTIONS=100
REDIS_MAXMEMORY=256mb

# =============================================================================
# MONITORING SETTINGS
# =============================================================================

# Grafana
GRAFANA_USER=admin
GRAFANA_PASSWORD=your-secure-grafana-password-here

# Sentry (Error Tracking)
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

# Log Levels
LOG_LEVEL=INFO
DJANGO_LOG_LEVEL=INFO
SQL_LOG_LEVEL=WARNING

# External Logging Services
# PAPERTRAIL_URL=logs.papertrailapp.com:12345
# LOGENTRIES_TOKEN=your-logentries-token

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Cache Settings
CACHE_TTL=300
SESSION_CACHE_TTL=3600

# Database Connection Pool
DB_CONN_MAX_AGE=600
DB_CONN_HEALTH_CHECKS=True

# Gunicorn Settings
GUNICORN_WORKERS=3
GUNICORN_TIMEOUT=30
GUNICORN_KEEPALIVE=2
GUNICORN_MAX_REQUESTS=1000

# =============================================================================
# BACKUP SETTINGS
# =============================================================================

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Google Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles
ENABLE_ANALYTICS=True
ENABLE_NOTIFICATIONS=True
ENABLE_CACHING=True
ENABLE_RATE_LIMITING=True

# Maintenance Mode
MAINTENANCE_MODE=False
MAINTENANCE_MESSAGE=The site is temporarily down for maintenance.

# =============================================================================
# API SETTINGS
# =============================================================================

# API Rate Limiting
API_RATE_LIMIT=1000/hour
API_BURST_LIMIT=100/minute

# API Versioning
API_VERSION=v1
API_DEFAULT_VERSION=v1

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline'
CSP_STYLE_SRC='self' 'unsafe-inline'
CSP_IMG_SRC='self' data: https:
CSP_FONT_SRC='self' https:

# =============================================================================
# CELERY SETTINGS (if using background tasks)
# =============================================================================

# Celery Configuration
# CELERY_BROKER_URL=redis://:your-secure-redis-password-here@redis:6379/2
# CELERY_RESULT_BACKEND=redis://:your-secure-redis-password-here@redis:6379/3
# CELERY_TASK_SERIALIZER=json
# CELERY_ACCEPT_CONTENT=['json']
# CELERY_RESULT_SERIALIZER=json
# CELERY_TIMEZONE=UTC

# =============================================================================
# HEALTH CHECKS
# =============================================================================

# Health Check Settings
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=60

# External Health Check URLs
# PINGDOM_CHECK_URL=https://api.pingdom.com/api/3.1/checks
# UPTIME_ROBOT_API_KEY=your-uptime-robot-api-key

# =============================================================================
# DEPLOYMENT SETTINGS
# =============================================================================

# Deployment Configuration
DEPLOY_ENVIRONMENT=production
DEPLOY_VERSION=1.0.0
DEPLOY_TIMESTAMP=2024-01-01T00:00:00Z

# Blue-Green Deployment
# BLUE_GREEN_ENABLED=True
# CURRENT_SLOT=blue

# =============================================================================
# COMPLIANCE SETTINGS
# =============================================================================

# GDPR Compliance
GDPR_ENABLED=True
DATA_RETENTION_DAYS=365
COOKIE_CONSENT_REQUIRED=True

# Security Compliance
SECURITY_AUDIT_ENABLED=True
PASSWORD_POLICY_ENABLED=True
TWO_FACTOR_AUTH_ENABLED=False

# =============================================================================
# NOTES
# =============================================================================

# 1. Replace all placeholder values with actual production values
# 2. Keep this file secure and never commit it to version control
# 3. Use strong, unique passwords for all services
# 4. Enable SSL/TLS for all external communications
# 5. Regularly rotate secrets and passwords
# 6. Monitor all services and set up alerting
# 7. Implement proper backup and disaster recovery procedures
# 8. Follow security best practices for production deployments
