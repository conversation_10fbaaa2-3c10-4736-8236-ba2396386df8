// src/components/__tests__/CampaignCard.test.jsx
/**
 * Comprehensive tests for the CampaignCard component.
 * 
 * This test suite covers rendering, user interactions, error handling,
 * and accessibility features of the CampaignCard component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import userEvent from '@testing-library/user-event';

import CampaignCard from '../campaigns/CampaignCard';
import { AuthProvider } from '../../contexts/AuthContext';

// Test utilities
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          {children}
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

const mockCampaign = {
  id: 1,
  title: 'Test Campaign',
  description: 'Test campaign description',
  start_date: '2024-12-01',
  end_date: '2024-12-31',
  created_at: '2024-01-01T00:00:00Z',
  hr_manager: 1,
  employee_count: 10,
  pairs_count: 5,
  current_step: 2,
  completed_steps: [1],
  completion_percentage: 20,
};

describe('CampaignCard', () => {
  let mockOnEdit;
  let mockOnDelete;
  let mockOnView;
  let user;

  beforeEach(() => {
    mockOnEdit = jest.fn();
    mockOnDelete = jest.fn();
    mockOnView = jest.fn();
    user = userEvent.setup();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders campaign information correctly', () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText('Test Campaign')).toBeInTheDocument();
      expect(screen.getByText('Test campaign description')).toBeInTheDocument();
      expect(screen.getByText(/December 1, 2024/)).toBeInTheDocument();
      expect(screen.getByText(/December 31, 2024/)).toBeInTheDocument();
      expect(screen.getByText('10 employees')).toBeInTheDocument();
      expect(screen.getByText('5 pairs')).toBeInTheDocument();
    });

    it('renders workflow progress correctly', () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText(/Step 2/)).toBeInTheDocument();
      expect(screen.getByText(/20% complete/)).toBeInTheDocument();
    });

    it('renders action buttons', () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByRole('button', { name: /view/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
    });

    it('renders with minimal campaign data', () => {
      const minimalCampaign = {
        id: 1,
        title: 'Minimal Campaign',
        start_date: '2024-12-01',
        end_date: '2024-12-31',
        employee_count: 0,
        pairs_count: 0,
        current_step: 1,
        completed_steps: [],
      };

      render(
        <CampaignCard
          campaign={minimalCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText('Minimal Campaign')).toBeInTheDocument();
      expect(screen.getByText('0 employees')).toBeInTheDocument();
      expect(screen.getByText('0 pairs')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls onView when view button is clicked', async () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const viewButton = screen.getByRole('button', { name: /view/i });
      await user.click(viewButton);

      expect(mockOnView).toHaveBeenCalledWith(mockCampaign.id);
      expect(mockOnView).toHaveBeenCalledTimes(1);
    });

    it('calls onEdit when edit button is clicked', async () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      expect(mockOnEdit).toHaveBeenCalledWith(mockCampaign.id);
      expect(mockOnEdit).toHaveBeenCalledTimes(1);
    });

    it('calls onDelete when delete button is clicked', async () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const deleteButton = screen.getByRole('button', { name: /delete/i });
      await user.click(deleteButton);

      expect(mockOnDelete).toHaveBeenCalledWith(mockCampaign.id);
      expect(mockOnDelete).toHaveBeenCalledTimes(1);
    });

    it('handles card click to view campaign', async () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
          clickable={true}
        />,
        { wrapper: createWrapper() }
      );

      const card = screen.getByTestId('campaign-card');
      await user.click(card);

      expect(mockOnView).toHaveBeenCalledWith(mockCampaign.id);
    });
  });

  describe('Loading States', () => {
    it('shows loading state when actions are processing', async () => {
      const slowOnEdit = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={slowOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      expect(editButton).toBeDisabled();
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

      await waitFor(() => {
        expect(editButton).not.toBeDisabled();
      });
    });

    it('disables all buttons during loading', async () => {
      const slowOnEdit = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={slowOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      const deleteButton = screen.getByRole('button', { name: /delete/i });
      const viewButton = screen.getByRole('button', { name: /view/i });

      await user.click(editButton);

      expect(editButton).toBeDisabled();
      expect(deleteButton).toBeDisabled();
      expect(viewButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('handles edit action errors gracefully', async () => {
      const errorOnEdit = jest.fn(() => Promise.reject(new Error('Edit failed')));

      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={errorOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      await waitFor(() => {
        expect(screen.getByText(/error occurred/i)).toBeInTheDocument();
      });

      expect(errorOnEdit).toHaveBeenCalledWith(mockCampaign.id);
    });

    it('handles delete action errors gracefully', async () => {
      const errorOnDelete = jest.fn(() => Promise.reject(new Error('Delete failed')));

      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={errorOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const deleteButton = screen.getByRole('button', { name: /delete/i });
      await user.click(deleteButton);

      await waitFor(() => {
        expect(screen.getByText(/error occurred/i)).toBeInTheDocument();
      });
    });

    it('shows retry button on error', async () => {
      const errorOnEdit = jest.fn(() => Promise.reject(new Error('Edit failed')));

      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={errorOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
      });

      const retryButton = screen.getByRole('button', { name: /retry/i });
      await user.click(retryButton);

      expect(errorOnEdit).toHaveBeenCalledTimes(2);
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByRole('button', { name: /view test campaign/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /edit test campaign/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /delete test campaign/i })).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const viewButton = screen.getByRole('button', { name: /view/i });
      const editButton = screen.getByRole('button', { name: /edit/i });

      // Tab to view button
      await user.tab();
      expect(viewButton).toHaveFocus();

      // Tab to edit button
      await user.tab();
      expect(editButton).toHaveFocus();

      // Press Enter to trigger action
      await user.keyboard('{Enter}');
      expect(mockOnEdit).toHaveBeenCalledWith(mockCampaign.id);
    });

    it('has proper heading hierarchy', () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      const heading = screen.getByRole('heading', { name: 'Test Campaign' });
      expect(heading).toBeInTheDocument();
      expect(heading.tagName).toBe('H3');
    });
  });

  describe('Visual States', () => {
    it('shows completed campaign state', () => {
      const completedCampaign = {
        ...mockCampaign,
        current_step: 5,
        completed_steps: [1, 2, 3, 4, 5],
        completion_percentage: 100,
      };

      render(
        <CampaignCard
          campaign={completedCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText(/completed/i)).toBeInTheDocument();
      expect(screen.getByText(/100% complete/i)).toBeInTheDocument();
    });

    it('shows active campaign state', () => {
      render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText(/in progress/i)).toBeInTheDocument();
    });

    it('shows draft campaign state', () => {
      const draftCampaign = {
        ...mockCampaign,
        current_step: 1,
        completed_steps: [],
        completion_percentage: 0,
      };

      render(
        <CampaignCard
          campaign={draftCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText(/draft/i)).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('memoizes expensive calculations', () => {
      const { rerender } = render(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      // Re-render with same props
      rerender(
        <CampaignCard
          campaign={mockCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />
      );

      // Component should not re-render unnecessarily
      expect(screen.getByText('Test Campaign')).toBeInTheDocument();
    });

    it('handles large numbers gracefully', () => {
      const largeCampaign = {
        ...mockCampaign,
        employee_count: 10000,
        pairs_count: 5000,
      };

      render(
        <CampaignCard
          campaign={largeCampaign}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onView={mockOnView}
        />,
        { wrapper: createWrapper() }
      );

      expect(screen.getByText('10,000 employees')).toBeInTheDocument();
      expect(screen.getByText('5,000 pairs')).toBeInTheDocument();
    });
  });
});
