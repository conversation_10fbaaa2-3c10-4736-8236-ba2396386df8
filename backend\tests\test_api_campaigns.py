# tests/test_api_campaigns.py
"""
Comprehensive API tests for campaign endpoints.

This module tests all campaign-related API endpoints including CRUD operations,
workflow management, permissions, and error handling.
"""

import pytest
from datetime import date, timedelta
from django.urls import reverse
from rest_framework import status

from campaigns.models import Campaign, CampaignWorkflowState
from employees.models import Employee
from .test_base import APIBaseTestCase


@pytest.mark.api
class CampaignAPITestCase(APIBaseTestCase):
    """Test campaign API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.campaign_data = {
            'title': 'Test Campaign',
            'description': 'Test campaign description',
            'start_date': (date.today() + timedelta(days=1)).isoformat(),
            'end_date': (date.today() + timedelta(days=30)).isoformat()
        }
    
    def test_create_campaign_success(self):
        """Test successful campaign creation"""
        self.authenticate()
        
        response = self.client.post('/campaigns/', self.campaign_data)
        
        data = self.assert_api_success(response, status.HTTP_201_CREATED)
        self.assertEqual(data['data']['title'], 'Test Campaign')
        self.assertEqual(data['data']['hr_manager'], self.hr_manager.id)
        
        # Verify campaign was created in database
        campaign = Campaign.objects.get(id=data['data']['id'])
        self.assertEqual(campaign.title, 'Test Campaign')
        self.assertEqual(campaign.hr_manager, self.hr_manager)
        
        # Verify workflow state was created
        self.assertTrue(hasattr(campaign, 'workflow_state'))
        self.assertEqual(campaign.workflow_state.current_step, 2)
    
    def test_create_campaign_unauthenticated(self):
        """Test campaign creation without authentication"""
        response = self.client.post('/campaigns/', self.campaign_data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_create_campaign_validation_errors(self):
        """Test campaign creation with validation errors"""
        self.authenticate()
        
        invalid_data = {
            'title': '',  # Empty title
            'start_date': (date.today() + timedelta(days=30)).isoformat(),
            'end_date': (date.today() + timedelta(days=1)).isoformat()  # End before start
        }
        
        response = self.client.post('/campaigns/', invalid_data)
        
        data = self.assert_api_error(response, status.HTTP_400_BAD_REQUEST)
        self.assertIn('title', str(data))
        self.assertIn('end_date', str(data))
    
    def test_create_campaign_past_start_date(self):
        """Test campaign creation with past start date"""
        self.authenticate()
        
        invalid_data = self.campaign_data.copy()
        invalid_data['start_date'] = (date.today() - timedelta(days=1)).isoformat()
        
        response = self.client.post('/campaigns/', invalid_data)
        
        data = self.assert_api_error(response, status.HTTP_400_BAD_REQUEST)
        self.assertIn('start_date', str(data))
    
    def test_list_campaigns_authenticated(self):
        """Test campaign listing with authentication"""
        self.authenticate()
        
        # Create campaigns for different users
        campaign1 = self.create_campaign(title="User 1 Campaign")
        campaign2 = self.create_campaign(
            hr_manager=self.other_hr_manager,
            title="User 2 Campaign"
        )
        
        response = self.client.get('/campaigns/')
        
        data = self.assert_api_success(response)
        campaigns = data['data']['results'] if 'results' in data['data'] else data['data']
        
        # Should only see own campaigns
        self.assertEqual(len(campaigns), 1)
        self.assertEqual(campaigns[0]['id'], campaign1.id)
        self.assertEqual(campaigns[0]['title'], "User 1 Campaign")
    
    def test_list_campaigns_with_pagination(self):
        """Test campaign listing with pagination"""
        self.authenticate()
        
        # Create multiple campaigns
        for i in range(25):
            self.create_campaign(title=f"Campaign {i}")
        
        response = self.client.get('/campaigns/?page=1&page_size=10')
        
        data = self.assert_api_success(response)
        self.assertIn('results', data['data'])
        self.assertIn('count', data['data'])
        self.assertEqual(len(data['data']['results']), 10)
        self.assertEqual(data['data']['count'], 25)
    
    def test_get_campaign_by_id_success(self):
        """Test successful campaign retrieval by ID"""
        self.authenticate()
        
        campaign = self.create_campaign()
        
        response = self.client.get(f'/campaigns/{campaign.id}/')
        
        data = self.assert_api_success(response)
        self.assertEqual(data['data']['id'], campaign.id)
        self.assertEqual(data['data']['title'], campaign.title)
    
    def test_get_campaign_by_id_not_found(self):
        """Test campaign retrieval with non-existent ID"""
        self.authenticate()
        
        response = self.client.get('/campaigns/999/')
        
        self.assert_not_found(response)
    
    def test_get_campaign_permission_denied(self):
        """Test campaign retrieval with wrong user"""
        self.authenticate()
        
        # Create campaign for other user
        other_campaign = self.create_campaign(hr_manager=self.other_hr_manager)
        
        response = self.client.get(f'/campaigns/{other_campaign.id}/')
        
        self.assert_not_found(response)  # Should return 404 to hide existence
    
    def test_update_campaign_success(self):
        """Test successful campaign update"""
        self.authenticate()
        
        campaign = self.create_campaign()
        
        update_data = {
            'title': 'Updated Campaign Title',
            'description': 'Updated description'
        }
        
        response = self.client.patch(f'/campaigns/{campaign.id}/', update_data)
        
        data = self.assert_api_success(response)
        self.assertEqual(data['data']['title'], 'Updated Campaign Title')
        self.assertEqual(data['data']['description'], 'Updated description')
        
        # Verify in database
        campaign.refresh_from_db()
        self.assertEqual(campaign.title, 'Updated Campaign Title')
    
    def test_update_campaign_validation_error(self):
        """Test campaign update with validation error"""
        self.authenticate()
        
        campaign = self.create_campaign()
        
        invalid_data = {
            'start_date': (date.today() + timedelta(days=30)).isoformat(),
            'end_date': (date.today() + timedelta(days=1)).isoformat()  # End before start
        }
        
        response = self.client.patch(f'/campaigns/{campaign.id}/', invalid_data)
        
        data = self.assert_api_error(response, status.HTTP_400_BAD_REQUEST)
        self.assertIn('end_date', str(data))
    
    def test_delete_campaign_success(self):
        """Test successful campaign deletion"""
        self.authenticate()
        
        campaign = self.create_campaign()
        
        response = self.client.delete(f'/campaigns/{campaign.id}/')
        
        self.assert_api_success(response, status.HTTP_204_NO_CONTENT)
        
        # Verify campaign was deleted
        self.assertFalse(Campaign.objects.filter(id=campaign.id).exists())
    
    def test_delete_campaign_with_employees(self):
        """Test campaign deletion with employees (should fail)"""
        self.authenticate()
        
        campaign = self.create_campaign()
        self.create_employee(campaign=campaign)
        
        response = self.client.delete(f'/campaigns/{campaign.id}/')
        
        data = self.assert_api_error(response, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertIn('employees', str(data).lower())
        
        # Verify campaign still exists
        self.assertTrue(Campaign.objects.filter(id=campaign.id).exists())
    
    def test_campaign_workflow_status(self):
        """Test campaign workflow status endpoint"""
        self.authenticate()
        
        campaign = self.create_campaign()
        
        response = self.client.get(f'/campaigns/{campaign.id}/workflow-status/')
        
        data = self.assert_api_success(response)
        self.assertEqual(data['data']['current_step'], 2)
        self.assertEqual(data['data']['completed_steps'], [1])
        self.assertIn('step_data', data['data'])
    
    def test_campaign_workflow_step_update(self):
        """Test campaign workflow step update"""
        self.authenticate()
        
        campaign = self.create_campaign()
        
        step_data = {
            'step': 2,
            'completed': True,
            'data': {'employees_uploaded': 5}
        }
        
        response = self.client.post(f'/campaigns/{campaign.id}/workflow-step/', step_data)
        
        data = self.assert_api_success(response)
        self.assertIn(2, data['data']['completed_steps'])
        self.assertEqual(data['data']['step_data']['2']['employees_uploaded'], 5)
    
    def test_campaign_statistics(self):
        """Test campaign statistics calculation"""
        self.authenticate()
        
        campaign = self.create_campaign()
        
        # Add some employees
        self.create_employee(campaign=campaign, name="Employee 1", email="<EMAIL>")
        self.create_employee(campaign=campaign, name="Employee 2", email="<EMAIL>")
        
        # Add some pairs
        self.create_employee_pair(campaign=campaign)
        
        response = self.client.get(f'/campaigns/{campaign.id}/')
        
        data = self.assert_api_success(response)
        self.assertEqual(data['data']['employee_count'], 2)
        self.assertEqual(data['data']['pairs_count'], 1)
    
    def test_campaign_search(self):
        """Test campaign search functionality"""
        self.authenticate()
        
        campaign1 = self.create_campaign(title="Coffee Meeting Campaign")
        campaign2 = self.create_campaign(title="Team Building Event")
        
        response = self.client.get('/campaigns/?search=coffee')
        
        data = self.assert_api_success(response)
        campaigns = data['data']['results'] if 'results' in data['data'] else data['data']
        
        self.assertEqual(len(campaigns), 1)
        self.assertEqual(campaigns[0]['id'], campaign1.id)
    
    def test_campaign_ordering(self):
        """Test campaign ordering"""
        self.authenticate()
        
        # Create campaigns with different dates
        campaign1 = self.create_campaign(title="First Campaign")
        campaign2 = self.create_campaign(title="Second Campaign")
        
        response = self.client.get('/campaigns/?ordering=-created_at')
        
        data = self.assert_api_success(response)
        campaigns = data['data']['results'] if 'results' in data['data'] else data['data']
        
        # Should be ordered by creation date (newest first)
        self.assertEqual(campaigns[0]['id'], campaign2.id)
        self.assertEqual(campaigns[1]['id'], campaign1.id)
    
    @pytest.mark.performance
    def test_campaign_list_performance(self):
        """Test campaign list endpoint performance"""
        self.authenticate()
        
        # Create many campaigns
        for i in range(100):
            self.create_campaign(title=f"Performance Test Campaign {i}")
        
        with self.measure_time('campaign_list'):
            response = self.client.get('/campaigns/')
        
        self.assert_api_success(response)
        
        # Should complete within reasonable time
        self.assert_performance_threshold('campaign_list', 2.0)  # 2 seconds max
    
    @pytest.mark.security
    def test_campaign_sql_injection_protection(self):
        """Test protection against SQL injection in search"""
        self.authenticate()
        
        malicious_search = "'; DROP TABLE campaigns_campaign; --"
        
        response = self.client.get(f'/campaigns/?search={malicious_search}')
        
        # Should not cause an error and should return empty results
        data = self.assert_api_success(response)
        campaigns = data['data']['results'] if 'results' in data['data'] else data['data']
        self.assertEqual(len(campaigns), 0)
        
        # Verify table still exists by creating a campaign
        test_campaign = self.create_campaign()
        self.assertIsNotNone(test_campaign.id)
