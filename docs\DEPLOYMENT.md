# Coffee Meetings Platform - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Coffee Meetings Platform in various environments, from development to production.

## Prerequisites

### System Requirements

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+

#### Recommended Requirements (Production)
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **OS**: Ubuntu 22.04 LTS

### Software Dependencies

#### Backend Dependencies
- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- Nginx (for production)

#### Frontend Dependencies
- Node.js 18+
- npm 8+

## Development Environment Setup

### 1. Clone Repository
```bash
git clone https://github.com/your-org/coffee-meetings-platform.git
cd coffee-meetings-platform
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
# Edit .env with your configuration

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver
```

### 3. Frontend Setup
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Create environment file
cp .env.example .env
# Edit .env with your configuration

# Start development server
npm start
```

### 4. Database Setup

#### PostgreSQL Installation (Ubuntu)
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE coffee_meetings;
CREATE USER coffee_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE coffee_meetings TO coffee_user;
\q
```

#### Redis Installation (Ubuntu)
```bash
# Install Redis
sudo apt install redis-server

# Start Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis connection
redis-cli ping
```

## Production Deployment

### 1. Server Preparation

#### Update System
```bash
sudo apt update && sudo apt upgrade -y
```

#### Install System Dependencies
```bash
# Install required packages
sudo apt install -y python3 python3-pip python3-venv nodejs npm postgresql postgresql-contrib redis-server nginx certbot python3-certbot-nginx git

# Install Docker (optional, for containerized deployment)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 2. Application Deployment

#### Create Application User
```bash
sudo adduser --system --group --home /opt/coffee-meetings coffee-meetings
sudo mkdir -p /opt/coffee-meetings
sudo chown coffee-meetings:coffee-meetings /opt/coffee-meetings
```

#### Deploy Application Code
```bash
# Switch to application user
sudo -u coffee-meetings -i

# Clone repository
cd /opt/coffee-meetings
git clone https://github.com/your-org/coffee-meetings-platform.git app
cd app

# Setup backend
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Setup environment
cp .env.production .env
# Edit .env with production values

# Run migrations
python manage.py migrate
python manage.py collectstatic --noinput

# Setup frontend
cd ../frontend
npm ci --production
npm run build
```

### 3. Database Configuration

#### PostgreSQL Production Setup
```bash
# Create production database
sudo -u postgres psql
CREATE DATABASE coffee_meetings_prod;
CREATE USER coffee_prod WITH PASSWORD 'secure_production_password';
GRANT ALL PRIVILEGES ON DATABASE coffee_meetings_prod TO coffee_prod;

# Configure PostgreSQL for production
sudo nano /etc/postgresql/13/main/postgresql.conf
# Uncomment and modify:
# listen_addresses = 'localhost'
# max_connections = 100
# shared_buffers = 256MB

sudo systemctl restart postgresql
```

### 4. Web Server Configuration

#### Nginx Configuration
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/coffee-meetings

# Add configuration:
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Frontend static files
    location / {
        root /opt/coffee-meetings/app/frontend/build;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Django admin
    location /admin/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static files for Django
    location /static/ {
        alias /opt/coffee-meetings/app/backend/staticfiles/;
    }
    
    # Media files
    location /media/ {
        alias /opt/coffee-meetings/app/backend/media/;
    }
}

# Enable site
sudo ln -s /etc/nginx/sites-available/coffee-meetings /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 5. SSL Certificate Setup

#### Using Certbot (Let's Encrypt)
```bash
# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

### 6. Process Management

#### Systemd Service for Backend
```bash
# Create systemd service file
sudo nano /etc/systemd/system/coffee-meetings.service

# Add service configuration:
[Unit]
Description=Coffee Meetings Platform
After=network.target

[Service]
Type=exec
User=coffee-meetings
Group=coffee-meetings
WorkingDirectory=/opt/coffee-meetings/app/backend
Environment=PATH=/opt/coffee-meetings/app/backend/venv/bin
ExecStart=/opt/coffee-meetings/app/backend/venv/bin/gunicorn coffee_meetings_platform.wsgi:application --bind 127.0.0.1:8000 --workers 3
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable coffee-meetings
sudo systemctl start coffee-meetings
```

#### Gunicorn Configuration
```bash
# Create Gunicorn configuration
sudo -u coffee-meetings nano /opt/coffee-meetings/app/backend/gunicorn.conf.py

# Add configuration:
bind = "127.0.0.1:8000"
workers = 3
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

## Docker Deployment

### 1. Dockerfile for Backend
```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Collect static files
RUN python manage.py collectstatic --noinput

EXPOSE 8000

CMD ["gunicorn", "coffee_meetings_platform.wsgi:application", "--bind", "0.0.0.0:8000"]
```

### 2. Dockerfile for Frontend
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci

# Build application
COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 3. Docker Compose Configuration
```yaml
# docker-compose.yml
version: '3.8'

services:
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: coffee_meetings
      POSTGRES_USER: coffee_user
      POSTGRES_PASSWORD: coffee_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    environment:
      - DATABASE_URL=************************************************/coffee_meetings
      - REDIS_URL=redis://redis:6379/1
      - DEBUG=False
    depends_on:
      - db
      - redis
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
  static_volume:
  media_volume:
```

### 4. Docker Deployment Commands
```bash
# Build and start services
docker-compose up -d

# Run migrations
docker-compose exec backend python manage.py migrate

# Create superuser
docker-compose exec backend python manage.py createsuperuser

# View logs
docker-compose logs -f backend
```

## Environment Configuration

### 1. Backend Environment Variables (.env)
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/coffee_meetings
REDIS_URL=redis://localhost:6379/1

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Frontend URL
FRONTEND_URL=https://your-domain.com
```

### 2. Frontend Environment Variables (.env)
```bash
REACT_APP_API_URL=https://your-domain.com
REACT_APP_BACKEND_URL=https://your-domain.com
REACT_APP_FRONTEND_URL=https://your-domain.com
```

## Monitoring and Maintenance

### 1. Log Management
```bash
# View application logs
sudo journalctl -u coffee-meetings -f

# View Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Setup log rotation
sudo nano /etc/logrotate.d/coffee-meetings
```

### 2. Backup Strategy
```bash
# Database backup script
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
pg_dump -h localhost -U coffee_prod coffee_meetings_prod > $BACKUP_DIR/db_backup_$DATE.sql

# Backup media files
tar -czf $BACKUP_DIR/media_backup_$DATE.tar.gz /opt/coffee-meetings/app/backend/media/

# Remove old backups (keep last 7 days)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 3. Health Checks
```bash
# Create health check script
#!/bin/bash
# Check if services are running
systemctl is-active --quiet coffee-meetings && echo "Backend: OK" || echo "Backend: FAILED"
systemctl is-active --quiet nginx && echo "Nginx: OK" || echo "Nginx: FAILED"
systemctl is-active --quiet postgresql && echo "PostgreSQL: OK" || echo "PostgreSQL: FAILED"
systemctl is-active --quiet redis && echo "Redis: OK" || echo "Redis: FAILED"

# Check HTTP response
curl -f http://localhost/api/health/ > /dev/null 2>&1 && echo "API: OK" || echo "API: FAILED"
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
psql -h localhost -U coffee_prod -d coffee_meetings_prod

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-13-main.log
```

#### 2. Static Files Not Loading
```bash
# Collect static files
cd /opt/coffee-meetings/app/backend
source venv/bin/activate
python manage.py collectstatic --noinput

# Check Nginx configuration
sudo nginx -t
sudo systemctl reload nginx
```

#### 3. Permission Issues
```bash
# Fix file permissions
sudo chown -R coffee-meetings:coffee-meetings /opt/coffee-meetings/
sudo chmod -R 755 /opt/coffee-meetings/app/
```

### Performance Optimization

#### 1. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_campaigns_hr_manager_created ON campaigns_campaign(hr_manager_id, created_at);
CREATE INDEX CONCURRENTLY idx_employees_campaign_email ON employees_employee(campaign_id, email);
```

#### 2. Nginx Optimization
```nginx
# Add to Nginx configuration
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Enable HTTP/2
listen 443 ssl http2;
```

## Security Considerations

### 1. Firewall Configuration
```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. Security Headers
```nginx
# Add security headers to Nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### 3. Regular Updates
```bash
# Create update script
#!/bin/bash
sudo apt update && sudo apt upgrade -y
sudo systemctl restart coffee-meetings
sudo systemctl restart nginx
```

This deployment guide provides comprehensive instructions for deploying the Coffee Meetings Platform in production. Follow the security best practices and monitoring guidelines to ensure a stable and secure deployment.
