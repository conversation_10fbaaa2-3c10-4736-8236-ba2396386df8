from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # USED ENDPOINTS (Called by frontend)
    path('', views.NotificationListView.as_view(), name='notification-list'),
    path('unread-count/', views.unread_count_view, name='unread-count'),
    path('<uuid:notification_id>/mark-read/', views.mark_notification_read, name='mark-read'),

    # REMOVED UNUSED ENDPOINTS (Not called by frontend)
    # path('<uuid:pk>/', views.NotificationDetailView.as_view(), name='notification-detail'),
    # path('<uuid:notification_id>/mark-unread/', views.mark_notification_unread, name='mark-unread'),
    # path('mark-all-read/', views.mark_all_read, name='mark-all-read'),
    # path('bulk-mark-read/', views.bulk_mark_read, name='bulk-mark-read'),
    # path('bulk-delete/', views.bulk_delete, name='bulk-delete'),
    # path('stats/', views.notification_stats, name='notification-stats'),
]
