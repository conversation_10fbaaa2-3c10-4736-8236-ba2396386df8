# tests/test_campaign_service.py
"""
Comprehensive tests for campaign service functionality.

This module tests all aspects of campaign management including CRUD operations,
workflow management, validation, and business logic.
"""

import pytest
from datetime import date, timedelta
from django.test import TestCase
from django.db import IntegrityError

from campaigns.services import CampaignService, CampaignWorkflowService
from campaigns.models import Campaign, CampaignWorkflowState
from employees.models import Employee
from matching.models import EmployeePair
from utils.exceptions import (
    ValidationException, 
    BusinessLogicException, 
    ResourceNotFoundException,
    PermissionDeniedException
)
from .test_base import ServiceTestCase, APIBaseTestCase


class CampaignServiceTestCase(ServiceTestCase):
    """Test cases for CampaignService"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.hr_manager = self.create_hr_manager()
        self.other_hr_manager = self.create_hr_manager(
            name="Other HR",
            email="<EMAIL>",
            company="Other Company"
        )
        self.service = CampaignService(user=self.hr_manager)
        self.other_service = CampaignService(user=self.other_hr_manager)
    
    def create_hr_manager(self, **kwargs):
        """Create HR manager for testing"""
        from users.models import HRManager
        defaults = {
            'name': 'Test HR Manager',
            'email': '<EMAIL>',
            'company_name': 'Test Company',
            'password_hash': 'hashed_password'
        }
        defaults.update(kwargs)
        return HRManager.objects.create(**defaults)
    
    def test_create_campaign_success(self):
        """Test successful campaign creation"""
        data = {
            'title': 'Test Campaign',
            'description': 'Test description',
            'start_date': date.today() + timedelta(days=1),
            'end_date': date.today() + timedelta(days=30)
        }
        
        campaign = self.service.create(data)
        
        self.assertIsNotNone(campaign)
        self.assertEqual(campaign.title, 'Test Campaign')
        self.assertEqual(campaign.hr_manager, self.hr_manager)
        
        # Check workflow state was created
        self.assertTrue(hasattr(campaign, 'workflow_state'))
        workflow_state = campaign.workflow_state
        self.assertEqual(workflow_state.current_step, 2)
        self.assertEqual(workflow_state.completed_steps, [1])
    
    def test_create_campaign_validation_errors(self):
        """Test campaign creation validation"""
        # Test end date before start date
        data = {
            'title': 'Test Campaign',
            'start_date': date.today() + timedelta(days=30),
            'end_date': date.today() + timedelta(days=1)
        }
        
        with self.assertRaises(ValidationException) as context:
            self.service.create(data)
        
        self.assertIn('end_date', str(context.exception.details))
    
    def test_create_campaign_past_start_date(self):
        """Test campaign creation with past start date"""
        data = {
            'title': 'Test Campaign',
            'start_date': date.today() - timedelta(days=1),
            'end_date': date.today() + timedelta(days=30)
        }
        
        with self.assertRaises(ValidationException) as context:
            self.service.create(data)
        
        self.assertIn('start_date', str(context.exception.details))
    
    def test_create_campaign_duplicate_title(self):
        """Test campaign creation with duplicate title"""
        data = {
            'title': 'Duplicate Campaign',
            'start_date': date.today() + timedelta(days=1),
            'end_date': date.today() + timedelta(days=30)
        }
        
        # Create first campaign
        self.service.create(data)
        
        # Try to create second campaign with same title
        with self.assertRaises(ValidationException) as context:
            self.service.create(data)
        
        self.assertIn('title', str(context.exception.details))
    
    def test_get_campaign_by_id_success(self):
        """Test successful campaign retrieval"""
        campaign = self.create_campaign()
        
        retrieved_campaign = self.service.get_by_id(campaign.id)
        
        self.assertEqual(retrieved_campaign.id, campaign.id)
        self.assertEqual(retrieved_campaign.title, campaign.title)
    
    def test_get_campaign_by_id_not_found(self):
        """Test campaign retrieval with non-existent ID"""
        with self.assertRaises(ResourceNotFoundException):
            self.service.get_by_id(999)
    
    def test_get_campaign_permission_denied(self):
        """Test campaign retrieval with wrong user"""
        campaign = self.create_campaign()
        
        with self.assertRaises(PermissionDeniedException):
            self.other_service.get_by_id(campaign.id)
    
    def test_update_campaign_success(self):
        """Test successful campaign update"""
        campaign = self.create_campaign()
        
        update_data = {
            'title': 'Updated Campaign',
            'description': 'Updated description'
        }
        
        updated_campaign = self.service.update(campaign.id, update_data)
        
        self.assertEqual(updated_campaign.title, 'Updated Campaign')
        self.assertEqual(updated_campaign.description, 'Updated description')
    
    def test_update_campaign_validation_error(self):
        """Test campaign update with validation error"""
        campaign = self.create_campaign()
        
        update_data = {
            'start_date': date.today() + timedelta(days=30),
            'end_date': date.today() + timedelta(days=1)  # End before start
        }
        
        with self.assertRaises(ValidationException):
            self.service.update(campaign.id, update_data)
    
    def test_list_campaigns_filtered_by_user(self):
        """Test campaign listing is filtered by user"""
        # Create campaigns for different users
        campaign1 = self.create_campaign(title="User 1 Campaign")
        campaign2 = self.create_campaign_for_other_user(title="User 2 Campaign")
        
        # Get campaigns for first user
        campaigns = self.service.list()
        
        self.assertEqual(len(campaigns), 1)
        self.assertEqual(campaigns[0].id, campaign1.id)
    
    def test_get_campaign_statistics(self):
        """Test campaign statistics calculation"""
        campaign = self.create_campaign()
        
        # Add some employees
        self.create_employee(campaign=campaign, name="Employee 1", email="<EMAIL>")
        self.create_employee(campaign=campaign, name="Employee 2", email="<EMAIL>")
        
        # Add some pairs
        self.create_employee_pair(campaign=campaign)
        
        stats = self.service.get_campaign_statistics(campaign.id)
        
        self.assertEqual(stats['campaign_id'], campaign.id)
        self.assertEqual(stats['employee_count'], 2)
        self.assertEqual(stats['pairs_count'], 1)
        self.assertEqual(stats['current_step'], 2)
        self.assertEqual(stats['completed_steps'], [1])
    
    def test_can_delete_campaign_with_employees(self):
        """Test campaign deletion validation with employees"""
        campaign = self.create_campaign()
        self.create_employee(campaign=campaign)
        
        can_delete, message = self.service.can_delete_campaign(campaign.id)
        
        self.assertFalse(can_delete)
        self.assertIn('employees', message.lower())
    
    def test_can_delete_campaign_with_pairs(self):
        """Test campaign deletion validation with pairs"""
        campaign = self.create_campaign()
        self.create_employee_pair(campaign=campaign)
        
        can_delete, message = self.service.can_delete_campaign(campaign.id)
        
        self.assertFalse(can_delete)
        self.assertIn('pairs', message.lower())
    
    def test_can_delete_empty_campaign(self):
        """Test campaign deletion validation for empty campaign"""
        campaign = self.create_campaign()
        
        can_delete, message = self.service.can_delete_campaign(campaign.id)
        
        self.assertTrue(can_delete)
        self.assertIn('can be deleted', message.lower())
    
    def test_delete_campaign_with_validation(self):
        """Test campaign deletion with business logic validation"""
        campaign = self.create_campaign()
        self.create_employee(campaign=campaign)
        
        with self.assertRaises(BusinessLogicException):
            self.service.delete(campaign.id)
    
    def create_campaign(self, **kwargs):
        """Helper to create campaign"""
        defaults = {
            'title': 'Test Campaign',
            'description': 'Test description',
            'start_date': date.today() + timedelta(days=1),
            'end_date': date.today() + timedelta(days=30),
            'hr_manager': self.hr_manager
        }
        defaults.update(kwargs)
        return Campaign.objects.create(**defaults)
    
    def create_campaign_for_other_user(self, **kwargs):
        """Helper to create campaign for other user"""
        defaults = {
            'hr_manager': self.other_hr_manager
        }
        defaults.update(kwargs)
        return self.create_campaign(**defaults)
    
    def create_employee(self, **kwargs):
        """Helper to create employee"""
        defaults = {
            'name': 'Test Employee',
            'email': '<EMAIL>',
            'arrival_date': date.today() - timedelta(days=30)
        }
        defaults.update(kwargs)
        return Employee.objects.create(**defaults)
    
    def create_employee_pair(self, **kwargs):
        """Helper to create employee pair"""
        campaign = kwargs.get('campaign')
        if not campaign:
            campaign = self.create_campaign()
        
        employee1 = self.create_employee(campaign=campaign, name="Employee 1", email="<EMAIL>")
        employee2 = self.create_employee(campaign=campaign, name="Employee 2", email="<EMAIL>")
        
        defaults = {
            'campaign': campaign,
            'employee1': employee1,
            'employee2': employee2,
            'created_by': 'test_user'
        }
        defaults.update(kwargs)
        return EmployeePair.objects.create(**defaults)


class CampaignWorkflowServiceTestCase(ServiceTestCase):
    """Test cases for CampaignWorkflowService"""
    
    def setUp(self):
        """Set up test environment"""
        super().setUp()
        self.hr_manager = self.create_hr_manager()
        self.service = CampaignWorkflowService(user=self.hr_manager)
        self.campaign = self.create_campaign()
    
    def create_hr_manager(self, **kwargs):
        """Create HR manager for testing"""
        from users.models import HRManager
        defaults = {
            'name': 'Test HR Manager',
            'email': '<EMAIL>',
            'company_name': 'Test Company',
            'password_hash': 'hashed_password'
        }
        defaults.update(kwargs)
        return HRManager.objects.create(**defaults)
    
    def create_campaign(self, **kwargs):
        """Helper to create campaign"""
        defaults = {
            'title': 'Test Campaign',
            'description': 'Test description',
            'start_date': date.today() + timedelta(days=1),
            'end_date': date.today() + timedelta(days=30),
            'hr_manager': self.hr_manager
        }
        defaults.update(kwargs)
        return Campaign.objects.create(**defaults)
    
    def test_get_workflow_state_creates_if_not_exists(self):
        """Test workflow state creation if it doesn't exist"""
        workflow_state = self.service.get_workflow_state(self.campaign.id)
        
        self.assertIsNotNone(workflow_state)
        self.assertEqual(workflow_state.campaign, self.campaign)
        self.assertEqual(workflow_state.current_step, 2)
        self.assertEqual(workflow_state.completed_steps, [1])
    
    def test_update_step_completion(self):
        """Test step completion update"""
        step_data = {'employees_uploaded': 5}
        
        workflow_state = self.service.update_step(
            self.campaign.id, 
            step_number=2, 
            completed=True, 
            step_data=step_data
        )
        
        self.assertIn(2, workflow_state.completed_steps)
        self.assertEqual(workflow_state.step_data['2'], step_data)
    
    def test_validate_step_completion_insufficient_employees(self):
        """Test step validation with insufficient employees"""
        can_complete, message = self.service.validate_step_completion(self.campaign.id, 2)
        
        self.assertFalse(can_complete)
        self.assertIn('2 employees', message)
    
    def test_reset_workflow(self):
        """Test workflow reset functionality"""
        # Complete some steps first
        workflow_state = self.service.get_workflow_state(self.campaign.id)
        workflow_state.completed_steps = [1, 2, 3]
        workflow_state.current_step = 4
        workflow_state.save()
        
        # Reset workflow
        reset_state = self.service.reset_workflow(self.campaign.id)
        
        self.assertEqual(reset_state.current_step, 2)
        self.assertEqual(reset_state.completed_steps, [1])
        self.assertIn('1', reset_state.step_data)  # Step 1 data should be preserved
