// src/setupTests.js
/**
 * Test setup configuration for the Coffee Meetings Platform frontend.
 * 
 * This file configures the testing environment with necessary polyfills,
 * mocks, and global test utilities.
 */

import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import { server } from './mocks/server';

// Configure testing library
configure({
  testIdAttribute: 'data-testid',
  asyncUtilTimeout: 5000,
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock fetch if not available
if (!global.fetch) {
  global.fetch = jest.fn();
}

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// Mock File and FileReader
global.File = class File {
  constructor(chunks, filename, options = {}) {
    this.chunks = chunks;
    this.name = filename;
    this.size = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
    this.type = options.type || '';
    this.lastModified = options.lastModified || Date.now();
  }
};

global.FileReader = class FileReader {
  constructor() {
    this.readyState = 0;
    this.result = null;
    this.error = null;
    this.onload = null;
    this.onerror = null;
    this.onabort = null;
  }

  readAsText(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'mocked file content';
      if (this.onload) this.onload({ target: this });
    }, 0);
  }

  readAsDataURL(file) {
    setTimeout(() => {
      this.readyState = 2;
      this.result = 'data:text/plain;base64,bW9ja2VkIGZpbGUgY29udGVudA==';
      if (this.onload) this.onload({ target: this });
    }, 0);
  }
};

// Mock Chart.js
jest.mock('chart.js', () => ({
  Chart: {
    register: jest.fn(),
  },
  CategoryScale: jest.fn(),
  LinearScale: jest.fn(),
  BarElement: jest.fn(),
  LineElement: jest.fn(),
  PointElement: jest.fn(),
  Title: jest.fn(),
  Tooltip: jest.fn(),
  Legend: jest.fn(),
}));

// Mock react-chartjs-2
jest.mock('react-chartjs-2', () => ({
  Bar: ({ data, options, ...props }) => (
    <div data-testid="bar-chart" {...props}>
      Bar Chart: {data?.datasets?.[0]?.label || 'No data'}
    </div>
  ),
  Line: ({ data, options, ...props }) => (
    <div data-testid="line-chart" {...props}>
      Line Chart: {data?.datasets?.[0]?.label || 'No data'}
    </div>
  ),
  Doughnut: ({ data, options, ...props }) => (
    <div data-testid="doughnut-chart" {...props}>
      Doughnut Chart: {data?.datasets?.[0]?.label || 'No data'}
    </div>
  ),
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useLocation: () => ({
    pathname: '/test',
    search: '',
    hash: '',
    state: null,
  }),
  useParams: () => ({}),
}));

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'PPP') return 'January 1, 2024';
    if (formatStr === 'yyyy-MM-dd') return '2024-01-01';
    return '2024-01-01';
  }),
  parseISO: jest.fn((dateStr) => new Date(dateStr)),
  isValid: jest.fn(() => true),
  addDays: jest.fn((date, days) => new Date(date.getTime() + days * 24 * 60 * 60 * 1000)),
  subDays: jest.fn((date, days) => new Date(date.getTime() - days * 24 * 60 * 60 * 1000)),
}));

// Setup MSW (Mock Service Worker)
beforeAll(() => {
  // Start the mock server
  server.listen({
    onUnhandledRequest: 'warn',
  });
});

afterEach(() => {
  // Reset any request handlers that are declared as a part of our tests
  server.resetHandlers();
  
  // Clear all mocks
  jest.clearAllMocks();
  
  // Clear localStorage and sessionStorage
  localStorageMock.clear();
  sessionStorageMock.clear();
});

afterAll(() => {
  // Clean up after the tests are finished
  server.close();
});

// Global test utilities
global.testUtils = {
  // Mock user data
  mockUser: {
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    company_name: 'Test Company',
  },

  // Mock campaign data
  mockCampaign: {
    id: 1,
    title: 'Test Campaign',
    description: 'Test campaign description',
    start_date: '2024-12-01',
    end_date: '2024-12-31',
    created_at: '2024-01-01T00:00:00Z',
    hr_manager: 1,
    employee_count: 10,
    pairs_count: 5,
    current_step: 2,
    completed_steps: [1],
  },

  // Mock employee data
  mockEmployee: {
    id: 1,
    name: 'Test Employee',
    email: '<EMAIL>',
    arrival_date: '2024-01-01',
    campaign: 1,
  },

  // Mock API responses
  mockApiResponse: (data, success = true) => ({
    success,
    data,
    message: success ? 'Operation successful' : 'Operation failed',
    timestamp: new Date().toISOString(),
  }),

  // Mock error response
  mockErrorResponse: (message = 'An error occurred', code = 'ERROR') => ({
    success: false,
    error: {
      code,
      message,
      details: {},
    },
    timestamp: new Date().toISOString(),
  }),

  // Wait for async operations
  waitFor: (callback, options = {}) => {
    return new Promise((resolve) => {
      const timeout = options.timeout || 1000;
      const interval = options.interval || 50;
      const startTime = Date.now();

      const check = () => {
        try {
          const result = callback();
          if (result) {
            resolve(result);
            return;
          }
        } catch (error) {
          // Continue waiting
        }

        if (Date.now() - startTime > timeout) {
          resolve(null);
          return;
        }

        setTimeout(check, interval);
      };

      check();
    });
  },

  // Create mock file
  createMockFile: (name = 'test.txt', content = 'test content', type = 'text/plain') => {
    return new File([content], name, { type });
  },

  // Create mock image file
  createMockImageFile: (name = 'test.jpg', type = 'image/jpeg') => {
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 100, 100);
    
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(new File([blob], name, { type }));
      }, type);
    });
  },
};

// Console error suppression for known issues
const originalError = console.error;
console.error = (...args) => {
  // Suppress specific warnings that are expected in tests
  const suppressedMessages = [
    'Warning: ReactDOM.render is deprecated',
    'Warning: findDOMNode is deprecated',
    'Warning: componentWillReceiveProps has been renamed',
  ];

  const message = args[0];
  if (typeof message === 'string' && suppressedMessages.some(msg => message.includes(msg))) {
    return;
  }

  originalError.call(console, ...args);
};

// Add custom matchers
expect.extend({
  toBeValidDate(received) {
    const pass = received instanceof Date && !isNaN(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid date`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid date`,
        pass: false,
      };
    }
  },

  toHaveValidationError(received, fieldName) {
    const hasError = received && received.errors && received.errors[fieldName];
    if (hasError) {
      return {
        message: () => `expected not to have validation error for field ${fieldName}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected to have validation error for field ${fieldName}`,
        pass: false,
      };
    }
  },
});
