// utils/validation.js
/**
 * Comprehensive validation utilities for the Coffee Meetings Platform.
 * 
 * This module provides reusable validation functions, form validation helpers,
 * and error message formatting for consistent validation across the application.
 */

/**
 * Email validation using RFC 5322 compliant regex
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
};

/**
 * Password strength validation
 */
export const validatePassword = (password) => {
  const errors = [];
  
  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    strength: calculatePasswordStrength(password)
  };
};

/**
 * Calculate password strength score (0-100)
 */
const calculatePasswordStrength = (password) => {
  let score = 0;
  
  // Length bonus
  score += Math.min(password.length * 4, 25);
  
  // Character variety bonus
  if (/[a-z]/.test(password)) score += 5;
  if (/[A-Z]/.test(password)) score += 5;
  if (/\d/.test(password)) score += 5;
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10;
  
  // Pattern penalties
  if (/(.)\1{2,}/.test(password)) score -= 10; // Repeated characters
  if (/123|abc|qwe/i.test(password)) score -= 10; // Common patterns
  
  return Math.max(0, Math.min(100, score));
};

/**
 * Date validation utilities
 */
export const dateValidation = {
  isValidDate: (dateString) => {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  },
  
  isAfterToday: (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date > today;
  },
  
  isAfterDate: (dateString, afterDateString) => {
    const date = new Date(dateString);
    const afterDate = new Date(afterDateString);
    return date > afterDate;
  },
  
  formatDate: (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
};

/**
 * Campaign validation rules
 */
export const validateCampaign = (campaignData) => {
  const errors = {};
  
  // Title validation
  if (!campaignData.title?.trim()) {
    errors.title = 'Campaign title is required';
  } else if (campaignData.title.trim().length < 3) {
    errors.title = 'Campaign title must be at least 3 characters long';
  } else if (campaignData.title.trim().length > 100) {
    errors.title = 'Campaign title must be less than 100 characters';
  }
  
  // Description validation
  if (campaignData.description && campaignData.description.length > 500) {
    errors.description = 'Description must be less than 500 characters';
  }
  
  // Start date validation
  if (!campaignData.start_date) {
    errors.start_date = 'Start date is required';
  } else if (!dateValidation.isValidDate(campaignData.start_date)) {
    errors.start_date = 'Invalid start date format';
  } else if (!dateValidation.isAfterToday(campaignData.start_date)) {
    errors.start_date = 'Start date must be in the future';
  }
  
  // End date validation
  if (!campaignData.end_date) {
    errors.end_date = 'End date is required';
  } else if (!dateValidation.isValidDate(campaignData.end_date)) {
    errors.end_date = 'Invalid end date format';
  } else if (campaignData.start_date && !dateValidation.isAfterDate(campaignData.end_date, campaignData.start_date)) {
    errors.end_date = 'End date must be after start date';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Employee validation rules
 */
export const validateEmployee = (employeeData) => {
  const errors = {};
  
  // Name validation
  if (!employeeData.name?.trim()) {
    errors.name = 'Employee name is required';
  } else if (employeeData.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters long';
  } else if (employeeData.name.trim().length > 100) {
    errors.name = 'Name must be less than 100 characters';
  }
  
  // Email validation
  if (!employeeData.email?.trim()) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(employeeData.email.trim())) {
    errors.email = 'Please enter a valid email address';
  }
  
  // Arrival date validation
  if (!employeeData.arrival_date) {
    errors.arrival_date = 'Arrival date is required';
  } else if (!dateValidation.isValidDate(employeeData.arrival_date)) {
    errors.arrival_date = 'Invalid arrival date format';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * User registration validation
 */
export const validateRegistration = (userData) => {
  const errors = {};
  
  // Full name validation
  if (!userData.fullName?.trim()) {
    errors.fullName = 'Full name is required';
  } else if (userData.fullName.trim().length < 2) {
    errors.fullName = 'Full name must be at least 2 characters long';
  }
  
  // Email validation
  if (!userData.email?.trim()) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(userData.email.trim())) {
    errors.email = 'Please enter a valid email address';
  }
  
  // Company name validation
  if (!userData.companyName?.trim()) {
    errors.companyName = 'Company name is required';
  } else if (userData.companyName.trim().length < 2) {
    errors.companyName = 'Company name must be at least 2 characters long';
  }
  
  // Password validation
  const passwordValidation = validatePassword(userData.password);
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.errors[0]; // Show first error
  }
  
  // Confirm password validation
  if (!userData.confirmPassword) {
    errors.confirmPassword = 'Please confirm your password';
  } else if (userData.password !== userData.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * File validation utilities
 */
export const fileValidation = {
  isValidImageFile: (file) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    return allowedTypes.includes(file.type);
  },
  
  isValidExcelFile: (file) => {
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    return allowedTypes.includes(file.type) || file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
  },
  
  isFileSizeValid: (file, maxSizeMB = 5) => {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
  },
  
  validateImageUpload: (file) => {
    const errors = [];
    
    if (!file) {
      errors.push('Please select a file');
      return { isValid: false, errors };
    }
    
    if (!fileValidation.isValidImageFile(file)) {
      errors.push('Please select a valid image file (JPEG, PNG, WebP)');
    }
    
    if (!fileValidation.isFileSizeValid(file, 5)) {
      errors.push('File size must be less than 5MB');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

/**
 * Form validation hook for React components
 */
export const useFormValidation = (initialValues, validationRules) => {
  const [values, setValues] = React.useState(initialValues);
  const [errors, setErrors] = React.useState({});
  const [touched, setTouched] = React.useState({});
  
  const validateField = (name, value) => {
    if (validationRules[name]) {
      const fieldErrors = validationRules[name](value, values);
      return fieldErrors;
    }
    return [];
  };
  
  const handleChange = (name, value) => {
    setValues(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };
  
  const handleBlur = (name) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    
    const fieldErrors = validateField(name, values[name]);
    if (fieldErrors.length > 0) {
      setErrors(prev => ({ ...prev, [name]: fieldErrors[0] }));
    }
  };
  
  const validateAll = () => {
    const newErrors = {};
    let isValid = true;
    
    Object.keys(validationRules).forEach(name => {
      const fieldErrors = validateField(name, values[name]);
      if (fieldErrors.length > 0) {
        newErrors[name] = fieldErrors[0];
        isValid = false;
      }
    });
    
    setErrors(newErrors);
    setTouched(Object.keys(validationRules).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    
    return isValid;
  };
  
  const reset = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  };
  
  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    validateAll,
    reset,
    isValid: Object.keys(errors).length === 0
  };
};

// Import React for the hook
import React from 'react';
