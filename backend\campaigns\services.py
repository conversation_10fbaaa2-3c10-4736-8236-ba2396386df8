# campaigns/services.py
"""
Business logic services for campaign management.

This module contains all business logic related to campaigns, including
workflow management, validation, and state transitions.
"""

from typing import Dict, Any, List, Optional
from django.db import transaction
from django.utils import timezone
from datetime import datetime, date

from utils.services import CRUDService, WorkflowService
from utils.exceptions import ValidationException, BusinessLogicException, ResourceNotFoundException
from .models import Campaign, CampaignWorkflowState
from employees.models import Employee
from matching.models import EmployeePair


class CampaignService(CRUDService):
    """
    Service class for campaign management with comprehensive business logic.
    """
    
    model_class = Campaign
    
    def get_queryset(self):
        """Filter campaigns by HR manager"""
        if self.user:
            return Campaign.objects.filter(hr_manager=self.user)
        return Campaign.objects.none()
    
    def validate_permissions(self, action: str, resource: Any = None):
        """Validate campaign permissions"""
        super().validate_permissions(action, resource)
        
        if resource and hasattr(resource, 'hr_manager'):
            if resource.hr_manager != self.user:
                raise PermissionDeniedException(action, f"Campaign {resource.id}")
        
        return True
    
    def validate_business_rules(self, data: Dict[str, Any], action: str = 'create'):
        """Validate campaign business rules"""
        errors = {}
        
        # Validate dates
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if start_date and end_date:
            if isinstance(start_date, str):
                start_date = datetime.fromisoformat(start_date).date()
            if isinstance(end_date, str):
                end_date = datetime.fromisoformat(end_date).date()
            
            if start_date >= end_date:
                errors['end_date'] = 'End date must be after start date'
            
            if action == 'create' and start_date < date.today():
                errors['start_date'] = 'Start date cannot be in the past'
        
        # Validate title uniqueness for user
        title = data.get('title')
        if title and self.user:
            existing_query = Campaign.objects.filter(
                title=title, 
                hr_manager=self.user
            )
            
            # For updates, exclude current object
            if action == 'update' and hasattr(self, '_current_obj_id'):
                existing_query = existing_query.exclude(id=self._current_obj_id)
            
            if existing_query.exists():
                errors['title'] = 'Campaign with this title already exists'
        
        if errors:
            raise ValidationException('Campaign validation failed', details=errors)
    
    @transaction.atomic
    def create(self, data: Dict[str, Any]) -> Campaign:
        """Create campaign with workflow state initialization"""
        # Add HR manager to data
        if self.user:
            data['hr_manager'] = self.user
        
        campaign = super().create(data)
        
        # Initialize workflow state
        CampaignWorkflowState.objects.create(
            campaign=campaign,
            current_step=2,  # Start from step 2 (Upload Employees)
            completed_steps=[1],  # Step 1 (Create Campaign) is completed
            step_data={
                '1': {
                    'title': campaign.title,
                    'description': campaign.description,
                    'start_date': campaign.start_date.isoformat(),
                    'end_date': campaign.end_date.isoformat(),
                    'created_at': campaign.created_at.isoformat()
                }
            }
        )
        
        self.log_action('campaign_created_with_workflow', {
            'campaign_id': campaign.id,
            'title': campaign.title
        })
        
        return campaign
    
    @transaction.atomic
    def update(self, obj_id: int, data: Dict[str, Any]) -> Campaign:
        """Update campaign with validation"""
        self._current_obj_id = obj_id  # For validation
        campaign = super().update(obj_id, data)
        
        # Update workflow step 1 data if basic info changed
        workflow_state = getattr(campaign, 'workflow_state', None)
        if workflow_state:
            step_1_data = workflow_state.step_data.get('1', {})
            step_1_data.update({
                'title': campaign.title,
                'description': campaign.description,
                'start_date': campaign.start_date.isoformat(),
                'end_date': campaign.end_date.isoformat(),
                'updated_at': timezone.now().isoformat()
            })
            workflow_state.step_data['1'] = step_1_data
            workflow_state.save()
        
        return campaign
    
    def get_campaign_statistics(self, campaign_id: int) -> Dict[str, Any]:
        """Get comprehensive campaign statistics"""
        campaign = self.get_by_id(campaign_id)
        
        # Get employee count
        employee_count = Employee.objects.filter(campaign=campaign).count()
        
        # Get pairs count
        pairs_count = EmployeePair.objects.filter(campaign=campaign).count()
        
        # Get workflow state
        workflow_state = getattr(campaign, 'workflow_state', None)
        current_step = workflow_state.current_step if workflow_state else 1
        completed_steps = workflow_state.completed_steps if workflow_state else []
        
        # Calculate completion percentage
        completion_percentage = (len(completed_steps) / 5) * 100
        
        return {
            'campaign_id': campaign.id,
            'title': campaign.title,
            'employee_count': employee_count,
            'pairs_count': pairs_count,
            'current_step': current_step,
            'completed_steps': completed_steps,
            'completion_percentage': completion_percentage,
            'is_completed': 5 in completed_steps,
            'created_at': campaign.created_at,
            'start_date': campaign.start_date,
            'end_date': campaign.end_date
        }
    
    def get_campaigns_with_statistics(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Get all campaigns with their statistics"""
        campaigns = self.list(filters)
        
        campaign_stats = []
        for campaign in campaigns:
            stats = self.get_campaign_statistics(campaign.id)
            campaign_stats.append(stats)
        
        return campaign_stats
    
    def can_delete_campaign(self, campaign_id: int) -> tuple[bool, str]:
        """Check if campaign can be deleted"""
        campaign = self.get_by_id(campaign_id)
        
        # Check if campaign has employees
        employee_count = Employee.objects.filter(campaign=campaign).count()
        if employee_count > 0:
            return False, f"Cannot delete campaign with {employee_count} employees. Remove employees first."
        
        # Check if campaign has pairs
        pairs_count = EmployeePair.objects.filter(campaign=campaign).count()
        if pairs_count > 0:
            return False, f"Cannot delete campaign with {pairs_count} employee pairs. Remove pairs first."
        
        return True, "Campaign can be deleted"
    
    @transaction.atomic
    def delete(self, obj_id: int) -> bool:
        """Delete campaign with validation"""
        can_delete, message = self.can_delete_campaign(obj_id)
        if not can_delete:
            raise BusinessLogicException(message)
        
        return super().delete(obj_id)


class CampaignWorkflowService(WorkflowService):
    """
    Service for managing campaign workflow state transitions.
    """
    
    def __init__(self, user=None):
        super().__init__(user)
        self.workflow_states = {
            1: [2],  # Create Campaign -> Upload Employees
            2: [3],  # Upload Employees -> Define Criteria
            3: [4],  # Define Criteria -> Generate Pairs
            4: [5],  # Generate Pairs -> Confirm and Send
            5: []    # Confirm and Send -> End (no further transitions)
        }
    
    def get_workflow_state(self, campaign_id: int) -> CampaignWorkflowState:
        """Get or create workflow state for campaign"""
        try:
            campaign = Campaign.objects.get(id=campaign_id, hr_manager=self.user)
        except Campaign.DoesNotExist:
            raise ResourceNotFoundException('Campaign', campaign_id)
        
        workflow_state, created = CampaignWorkflowState.objects.get_or_create(
            campaign=campaign,
            defaults={
                'current_step': 2,
                'completed_steps': [1],
                'step_data': {}
            }
        )
        
        if created:
            self.log_action('workflow_state_created', {'campaign_id': campaign_id})
        
        return workflow_state
    
    @transaction.atomic
    def update_step(self, campaign_id: int, step_number: int, completed: bool = True, step_data: Dict[str, Any] = None) -> CampaignWorkflowState:
        """Update workflow step with validation"""
        workflow_state = self.get_workflow_state(campaign_id)
        
        if completed:
            workflow_state.mark_step_completed(step_number, step_data)
            
            self.log_action('workflow_step_completed', {
                'campaign_id': campaign_id,
                'step_number': step_number,
                'step_data': step_data
            })
        
        return workflow_state
    
    def validate_step_completion(self, campaign_id: int, step_number: int) -> tuple[bool, str]:
        """Validate if step can be completed"""
        campaign = Campaign.objects.get(id=campaign_id, hr_manager=self.user)
        
        if step_number == 2:  # Upload Employees
            employee_count = Employee.objects.filter(campaign=campaign).count()
            if employee_count < 2:
                return False, "At least 2 employees are required to complete this step"
        
        elif step_number == 3:  # Define Criteria
            # Criteria are optional, so this step can always be completed
            pass
        
        elif step_number == 4:  # Generate Pairs
            pairs_count = EmployeePair.objects.filter(campaign=campaign).count()
            if pairs_count == 0:
                return False, "At least one employee pair must be generated"
        
        elif step_number == 5:  # Confirm and Send
            pairs_count = EmployeePair.objects.filter(campaign=campaign).count()
            if pairs_count == 0:
                return False, "No employee pairs to confirm and send"
        
        return True, "Step can be completed"
    
    @transaction.atomic
    def reset_workflow(self, campaign_id: int) -> CampaignWorkflowState:
        """Reset workflow to initial state"""
        workflow_state = self.get_workflow_state(campaign_id)
        
        workflow_state.current_step = 2
        workflow_state.completed_steps = [1]
        workflow_state.step_data = {
            '1': workflow_state.step_data.get('1', {})  # Keep step 1 data
        }
        workflow_state.save()
        
        self.log_action('workflow_reset', {'campaign_id': campaign_id})
        
        return workflow_state
