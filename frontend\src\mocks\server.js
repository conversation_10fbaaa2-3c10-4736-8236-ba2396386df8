// src/mocks/server.js
/**
 * Mock Service Worker (MSW) server configuration for testing.
 * 
 * This module provides mock API responses for testing the frontend
 * without requiring a real backend server.
 */

import { setupServer } from 'msw/node';
import { rest } from 'msw';

// Mock data
const mockUser = {
  id: 1,
  name: 'Test User',
  email: '<EMAIL>',
  company_name: 'Test Company',
};

const mockCampaigns = [
  {
    id: 1,
    title: 'Test Campaign 1',
    description: 'First test campaign',
    start_date: '2024-12-01',
    end_date: '2024-12-31',
    created_at: '2024-01-01T00:00:00Z',
    hr_manager: 1,
    employee_count: 10,
    pairs_count: 5,
    current_step: 2,
    completed_steps: [1],
  },
  {
    id: 2,
    title: 'Test Campaign 2',
    description: 'Second test campaign',
    start_date: '2024-11-01',
    end_date: '2024-11-30',
    created_at: '2024-01-02T00:00:00Z',
    hr_manager: 1,
    employee_count: 8,
    pairs_count: 4,
    current_step: 3,
    completed_steps: [1, 2],
  },
];

const mockEmployees = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    arrival_date: '2024-01-01',
    campaign: 1,
    attributes: {
      department: 'Engineering',
      level: 'Senior',
    },
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    arrival_date: '2024-01-15',
    campaign: 1,
    attributes: {
      department: 'Marketing',
      level: 'Junior',
    },
  },
];

const mockPairs = [
  {
    id: 1,
    employee1: mockEmployees[0],
    employee2: mockEmployees[1],
    campaign: 1,
    email_status: 'sent',
    email_sent_at: '2024-01-01T10:00:00Z',
    created_at: '2024-01-01T09:00:00Z',
  },
];

// Helper function to create standardized API responses
const createApiResponse = (data, success = true, message = null) => ({
  success,
  data,
  message,
  timestamp: new Date().toISOString(),
});

const createErrorResponse = (message, code = 'ERROR', details = {}) => ({
  success: false,
  error: {
    code,
    message,
    details,
  },
  timestamp: new Date().toISOString(),
});

// Request handlers
const handlers = [
  // Authentication endpoints
  rest.post('/users/login/', (req, res, ctx) => {
    const { email, password } = req.body;
    
    if (email === '<EMAIL>' && password === 'password') {
      return res(
        ctx.status(200),
        ctx.json(createApiResponse({
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          user: mockUser,
        }))
      );
    }
    
    return res(
      ctx.status(401),
      ctx.json(createErrorResponse('Invalid credentials', 'INVALID_CREDENTIALS'))
    );
  }),

  rest.post('/users/register/', (req, res, ctx) => {
    const { email } = req.body;
    
    if (email === '<EMAIL>') {
      return res(
        ctx.status(400),
        ctx.json(createErrorResponse('Email already exists', 'EMAIL_EXISTS'))
      );
    }
    
    return res(
      ctx.status(201),
      ctx.json(createApiResponse({
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        user: { ...mockUser, email },
      }))
    );
  }),

  rest.post('/users/refresh/', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createApiResponse({
        access_token: 'new-mock-access-token',
      }))
    );
  }),

  rest.get('/users/profile/', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res(
        ctx.status(401),
        ctx.json(createErrorResponse('Authentication required', 'AUTHENTICATION_REQUIRED'))
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json(createApiResponse(mockUser))
    );
  }),

  // Campaign endpoints
  rest.get('/campaigns/', (req, res, ctx) => {
    const url = new URL(req.url);
    const search = url.searchParams.get('search');
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('page_size') || '20');
    
    let filteredCampaigns = mockCampaigns;
    
    if (search) {
      filteredCampaigns = mockCampaigns.filter(campaign =>
        campaign.title.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedCampaigns = filteredCampaigns.slice(startIndex, endIndex);
    
    return res(
      ctx.status(200),
      ctx.json(createApiResponse({
        results: paginatedCampaigns,
        count: filteredCampaigns.length,
        next: endIndex < filteredCampaigns.length ? `?page=${page + 1}` : null,
        previous: page > 1 ? `?page=${page - 1}` : null,
      }))
    );
  }),

  rest.get('/campaigns/:id/', (req, res, ctx) => {
    const { id } = req.params;
    const campaign = mockCampaigns.find(c => c.id === parseInt(id));
    
    if (!campaign) {
      return res(
        ctx.status(404),
        ctx.json(createErrorResponse('Campaign not found', 'RESOURCE_NOT_FOUND'))
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json(createApiResponse(campaign))
    );
  }),

  rest.post('/campaigns/', (req, res, ctx) => {
    const campaignData = req.body;
    
    // Validate required fields
    if (!campaignData.title) {
      return res(
        ctx.status(400),
        ctx.json(createErrorResponse('Title is required', 'VALIDATION_ERROR', {
          title: ['This field is required.']
        }))
      );
    }
    
    const newCampaign = {
      id: mockCampaigns.length + 1,
      ...campaignData,
      hr_manager: mockUser.id,
      created_at: new Date().toISOString(),
      employee_count: 0,
      pairs_count: 0,
      current_step: 2,
      completed_steps: [1],
    };
    
    mockCampaigns.push(newCampaign);
    
    return res(
      ctx.status(201),
      ctx.json(createApiResponse(newCampaign, true, 'Campaign created successfully'))
    );
  }),

  rest.patch('/campaigns/:id/', (req, res, ctx) => {
    const { id } = req.params;
    const updates = req.body;
    const campaignIndex = mockCampaigns.findIndex(c => c.id === parseInt(id));
    
    if (campaignIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json(createErrorResponse('Campaign not found', 'RESOURCE_NOT_FOUND'))
      );
    }
    
    mockCampaigns[campaignIndex] = {
      ...mockCampaigns[campaignIndex],
      ...updates,
    };
    
    return res(
      ctx.status(200),
      ctx.json(createApiResponse(mockCampaigns[campaignIndex], true, 'Campaign updated successfully'))
    );
  }),

  rest.delete('/campaigns/:id/', (req, res, ctx) => {
    const { id } = req.params;
    const campaignIndex = mockCampaigns.findIndex(c => c.id === parseInt(id));
    
    if (campaignIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json(createErrorResponse('Campaign not found', 'RESOURCE_NOT_FOUND'))
      );
    }
    
    mockCampaigns.splice(campaignIndex, 1);
    
    return res(
      ctx.status(204)
    );
  }),

  // Employee endpoints
  rest.get('/employees/', (req, res, ctx) => {
    const url = new URL(req.url);
    const campaignId = url.searchParams.get('campaign');
    
    let filteredEmployees = mockEmployees;
    
    if (campaignId) {
      filteredEmployees = mockEmployees.filter(emp => 
        emp.campaign === parseInt(campaignId)
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json(createApiResponse(filteredEmployees))
    );
  }),

  rest.post('/employees/upload-excel/', (req, res, ctx) => {
    // Simulate file upload processing
    return res(
      ctx.delay(1000), // Simulate processing time
      ctx.status(200),
      ctx.json(createApiResponse({
        imported_count: 10,
        errors: [],
      }, true, '10 employees imported successfully'))
    );
  }),

  // Matching endpoints
  rest.get('/matching/campaigns/:id/pairs/', (req, res, ctx) => {
    const { id } = req.params;
    const campaignPairs = mockPairs.filter(pair => pair.campaign === parseInt(id));
    
    return res(
      ctx.status(200),
      ctx.json(createApiResponse(campaignPairs))
    );
  }),

  rest.post('/matching/campaigns/:id/generate-pairs/', (req, res, ctx) => {
    return res(
      ctx.delay(2000), // Simulate generation time
      ctx.status(200),
      ctx.json(createApiResponse({
        pairs: mockPairs,
        generated_count: mockPairs.length,
      }, true, 'Pairs generated successfully'))
    );
  }),

  // Dashboard endpoints
  rest.get('/dashboard/statistics/', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createApiResponse({
        total_campaigns: mockCampaigns.length,
        total_employees: mockEmployees.length,
        total_pairs: mockPairs.length,
        active_campaigns: mockCampaigns.filter(c => c.current_step < 5).length,
      }))
    );
  }),

  // Error simulation endpoints for testing
  rest.get('/test/error-500', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json(createErrorResponse('Internal server error', 'INTERNAL_SERVER_ERROR'))
    );
  }),

  rest.get('/test/network-error', (req, res, ctx) => {
    return res.networkError('Network connection failed');
  }),

  rest.get('/test/timeout', (req, res, ctx) => {
    return res(
      ctx.delay(10000) // 10 second delay to simulate timeout
    );
  }),
];

// Create and export the server
export const server = setupServer(...handlers);

// Export handlers for individual test customization
export { handlers };

// Export mock data for use in tests
export {
  mockUser,
  mockCampaigns,
  mockEmployees,
  mockPairs,
  createApiResponse,
  createErrorResponse,
};
