/**
 * Performance testing utilities for campaigns page optimization
 */

import { workflowService } from '../services/workflowService';

/**
 * Test the performance difference between individual and bulk requests
 */
export const testWorkflowPerformance = async (campaignIds) => {
  console.log('🧪 Starting workflow performance test...');
  
  // Clear cache to ensure fair comparison
  workflowService.clearCache();
  
  const results = {
    individual: { requests: 0, totalTime: 0, errors: 0 },
    bulk: { requests: 0, totalTime: 0, errors: 0 },
    cached: { requests: 0, totalTime: 0, hits: 0 }
  };

  // Test 1: Individual requests (old method)
  console.log('📡 Testing individual requests...');
  const individualStart = performance.now();
  
  for (const campaignId of campaignIds) {
    try {
      const start = performance.now();
      await workflowService.getCampaignWorkflowStatusDirect(campaignId);
      const end = performance.now();
      
      results.individual.requests++;
      results.individual.totalTime += (end - start);
    } catch (error) {
      results.individual.errors++;
      console.error(`Individual request failed for campaign ${campaignId}:`, error);
    }
  }
  
  const individualEnd = performance.now();
  results.individual.totalTime = individualEnd - individualStart;

  // Clear cache again
  workflowService.clearCache();

  // Test 2: Bulk request (new method)
  console.log('📦 Testing bulk request...');
  const bulkStart = performance.now();
  
  try {
    await workflowService.getBulkCampaignWorkflowStatus(campaignIds);
    const bulkEnd = performance.now();
    
    results.bulk.requests = 1;
    results.bulk.totalTime = bulkEnd - bulkStart;
  } catch (error) {
    results.bulk.errors++;
    console.error('Bulk request failed:', error);
  }

  // Test 3: Cached requests (after bulk load)
  console.log('💾 Testing cached requests...');
  const cachedStart = performance.now();
  
  for (const campaignId of campaignIds) {
    const start = performance.now();
    const cached = workflowService.getCachedWorkflowStatus(campaignId);
    const end = performance.now();
    
    results.cached.requests++;
    results.cached.totalTime += (end - start);
    
    if (cached) {
      results.cached.hits++;
    }
  }
  
  const cachedEnd = performance.now();
  results.cached.totalTime = cachedEnd - cachedStart;

  // Calculate improvements
  const improvements = {
    bulkVsIndividual: {
      timeReduction: ((results.individual.totalTime - results.bulk.totalTime) / results.individual.totalTime * 100).toFixed(1),
      requestReduction: ((results.individual.requests - results.bulk.requests) / results.individual.requests * 100).toFixed(1)
    },
    cacheHitRate: (results.cached.hits / results.cached.requests * 100).toFixed(1),
    cachedVsIndividual: {
      timeReduction: ((results.individual.totalTime - results.cached.totalTime) / results.individual.totalTime * 100).toFixed(1)
    }
  };

  // Display results
  console.log('📊 Performance Test Results:');
  console.table({
    'Individual Requests': {
      'Requests': results.individual.requests,
      'Total Time (ms)': results.individual.totalTime.toFixed(2),
      'Avg Time (ms)': (results.individual.totalTime / results.individual.requests).toFixed(2),
      'Errors': results.individual.errors
    },
    'Bulk Request': {
      'Requests': results.bulk.requests,
      'Total Time (ms)': results.bulk.totalTime.toFixed(2),
      'Avg Time (ms)': results.bulk.totalTime.toFixed(2),
      'Errors': results.bulk.errors
    },
    'Cached Requests': {
      'Requests': results.cached.requests,
      'Total Time (ms)': results.cached.totalTime.toFixed(2),
      'Avg Time (ms)': (results.cached.totalTime / results.cached.requests).toFixed(2),
      'Cache Hits': results.cached.hits
    }
  });

  console.log('🎯 Performance Improvements:');
  console.log(`• Bulk vs Individual: ${improvements.bulkVsIndividual.timeReduction}% faster, ${improvements.bulkVsIndividual.requestReduction}% fewer requests`);
  console.log(`• Cache Hit Rate: ${improvements.cacheHitRate}%`);
  console.log(`• Cached vs Individual: ${improvements.cachedVsIndividual.timeReduction}% faster`);

  return { results, improvements };
};

/**
 * Test batching behavior
 */
export const testBatchingBehavior = async (campaignIds) => {
  console.log('🔄 Testing batching behavior...');
  
  workflowService.clearCache();
  
  const startTime = performance.now();
  
  // Simulate multiple components requesting workflow status simultaneously
  const promises = campaignIds.map(id => 
    workflowService.getCampaignWorkflowStatus(id)
  );
  
  const results = await Promise.all(promises);
  const endTime = performance.now();
  
  console.log(`✅ Batching test completed in ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`📦 Processed ${campaignIds.length} campaigns`);
  console.log(`🎯 Results received: ${results.length}`);
  
  return {
    totalTime: endTime - startTime,
    campaignCount: campaignIds.length,
    resultsCount: results.length,
    avgTimePerCampaign: (endTime - startTime) / campaignIds.length
  };
};

/**
 * Run comprehensive performance test suite
 */
export const runPerformanceTestSuite = async (campaignIds = [1, 2, 3, 4, 5]) => {
  console.log('🚀 Starting comprehensive performance test suite...');
  console.log(`📋 Testing with ${campaignIds.length} campaigns: [${campaignIds.join(', ')}]`);
  
  try {
    // Test 1: Workflow performance comparison
    const workflowTest = await testWorkflowPerformance(campaignIds);
    
    // Test 2: Batching behavior
    const batchingTest = await testBatchingBehavior(campaignIds);
    
    // Summary
    console.log('🎉 Performance Test Suite Complete!');
    console.log('📈 Summary:');
    console.log(`• Bulk requests are ${workflowTest.improvements.bulkVsIndividual.timeReduction}% faster`);
    console.log(`• Cache hit rate: ${workflowTest.improvements.cacheHitRate}%`);
    console.log(`• Batching processed ${batchingTest.campaignCount} campaigns in ${batchingTest.totalTime.toFixed(2)}ms`);
    
    return {
      workflow: workflowTest,
      batching: batchingTest,
      summary: {
        bulkImprovement: workflowTest.improvements.bulkVsIndividual.timeReduction,
        cacheHitRate: workflowTest.improvements.cacheHitRate,
        batchingTime: batchingTest.totalTime
      }
    };
    
  } catch (error) {
    console.error('❌ Performance test suite failed:', error);
    throw error;
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.performanceTest = {
    testWorkflowPerformance,
    testBatchingBehavior,
    runPerformanceTestSuite
  };
}

export default {
  testWorkflowPerformance,
  testBatchingBehavior,
  runPerformanceTestSuite
};
