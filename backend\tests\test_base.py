# tests/test_base.py
"""
Base test classes and utilities for the Coffee Meetings Platform test suite.

This module provides comprehensive base classes for different types of tests,
including unit tests, integration tests, and API tests.
"""

import pytest
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import transaction
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from unittest.mock import Mock, patch
import json
from datetime import datetime, date, timedelta
from decimal import Decimal

from users.models import HRManager
from campaigns.models import Campaign, CampaignWorkflowState
from employees.models import Employee
from matching.models import EmployeePair, CampaignMatchingCriteria
from evaluations.models import Evaluation


class BaseTestCase(TestCase):
    """
    Base test case with common setup and utility methods.
    """
    
    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.clear_cache()
        self.setup_test_data()
    
    def tearDown(self):
        """Clean up after tests"""
        super().tearDown()
        self.clear_cache()
    
    def clear_cache(self):
        """Clear all cache data"""
        cache.clear()
    
    def setup_test_data(self):
        """Set up common test data - override in subclasses"""
        pass
    
    def assert_response_success(self, response, expected_status=status.HTTP_200_OK):
        """Assert that response is successful"""
        self.assertEqual(response.status_code, expected_status)
        if hasattr(response, 'data'):
            self.assertTrue(response.data.get('success', True))
    
    def assert_response_error(self, response, expected_status=status.HTTP_400_BAD_REQUEST, expected_code=None):
        """Assert that response contains error"""
        self.assertEqual(response.status_code, expected_status)
        if hasattr(response, 'data'):
            self.assertFalse(response.data.get('success', False))
            if expected_code:
                self.assertEqual(response.data.get('error', {}).get('code'), expected_code)
    
    def assert_field_error(self, response, field_name, expected_status=status.HTTP_400_BAD_REQUEST):
        """Assert that response contains field-specific error"""
        self.assert_response_error(response, expected_status)
        error_details = response.data.get('error', {}).get('details', {})
        self.assertIn(field_name, error_details)


class APIBaseTestCase(APITestCase):
    """
    Base test case for API testing with authentication and common utilities.
    """
    
    def setUp(self):
        """Set up API test environment"""
        super().setUp()
        self.client = APIClient()
        self.clear_cache()
        self.setup_test_data()
    
    def tearDown(self):
        """Clean up after API tests"""
        super().tearDown()
        self.clear_cache()
    
    def clear_cache(self):
        """Clear all cache data"""
        cache.clear()
    
    def setup_test_data(self):
        """Set up common test data"""
        self.hr_manager = self.create_hr_manager()
        self.other_hr_manager = self.create_hr_manager(
            name="Other HR Manager",
            email="<EMAIL>",
            company="Other Company"
        )
    
    def create_hr_manager(self, **kwargs):
        """Create HR manager for testing"""
        defaults = {
            'name': 'Test HR Manager',
            'email': '<EMAIL>',
            'company_name': 'Test Company',
            'password_hash': 'hashed_password'
        }
        defaults.update(kwargs)
        return HRManager.objects.create(**defaults)
    
    def create_campaign(self, hr_manager=None, **kwargs):
        """Create campaign for testing"""
        if hr_manager is None:
            hr_manager = self.hr_manager
        
        defaults = {
            'title': 'Test Campaign',
            'description': 'Test campaign description',
            'start_date': date.today() + timedelta(days=1),
            'end_date': date.today() + timedelta(days=30),
            'hr_manager': hr_manager
        }
        defaults.update(kwargs)
        return Campaign.objects.create(**defaults)
    
    def create_employee(self, campaign=None, **kwargs):
        """Create employee for testing"""
        if campaign is None:
            campaign = self.create_campaign()
        
        defaults = {
            'name': 'Test Employee',
            'email': '<EMAIL>',
            'arrival_date': date.today() - timedelta(days=30),
            'campaign': campaign
        }
        defaults.update(kwargs)
        return Employee.objects.create(**defaults)
    
    def create_employee_pair(self, campaign=None, **kwargs):
        """Create employee pair for testing"""
        if campaign is None:
            campaign = self.create_campaign()
        
        employee1 = self.create_employee(campaign=campaign, name="Employee 1", email="<EMAIL>")
        employee2 = self.create_employee(campaign=campaign, name="Employee 2", email="<EMAIL>")
        
        defaults = {
            'campaign': campaign,
            'employee1': employee1,
            'employee2': employee2,
            'created_by': 'test_user'
        }
        defaults.update(kwargs)
        return EmployeePair.objects.create(**defaults)
    
    def authenticate(self, user=None):
        """Authenticate user for API requests"""
        if user is None:
            user = self.hr_manager
        self.client.force_authenticate(user=user)
    
    def get_json_response(self, response):
        """Get JSON data from response"""
        if hasattr(response, 'data'):
            return response.data
        return json.loads(response.content.decode('utf-8'))
    
    def assert_api_success(self, response, expected_status=status.HTTP_200_OK):
        """Assert API response is successful"""
        self.assertEqual(response.status_code, expected_status)
        data = self.get_json_response(response)
        self.assertTrue(data.get('success', True))
        return data
    
    def assert_api_error(self, response, expected_status=status.HTTP_400_BAD_REQUEST, expected_code=None):
        """Assert API response contains error"""
        self.assertEqual(response.status_code, expected_status)
        data = self.get_json_response(response)
        self.assertFalse(data.get('success', False))
        if expected_code:
            self.assertEqual(data.get('error', {}).get('code'), expected_code)
        return data
    
    def assert_permission_denied(self, response):
        """Assert permission denied response"""
        self.assert_api_error(response, status.HTTP_403_FORBIDDEN, 'PERMISSION_DENIED')
    
    def assert_not_found(self, response):
        """Assert not found response"""
        self.assert_api_error(response, status.HTTP_404_NOT_FOUND, 'RESOURCE_NOT_FOUND')
    
    def assert_validation_error(self, response, field_name=None):
        """Assert validation error response"""
        data = self.assert_api_error(response, status.HTTP_400_BAD_REQUEST, 'VALIDATION_ERROR')
        if field_name:
            error_details = data.get('error', {}).get('details', {})
            self.assertIn(field_name, error_details)


class ServiceTestCase(BaseTestCase):
    """
    Base test case for service layer testing.
    """
    
    def setUp(self):
        """Set up service test environment"""
        super().setUp()
        self.setup_mocks()
    
    def setup_mocks(self):
        """Set up common mocks - override in subclasses"""
        pass
    
    def create_mock_user(self, **kwargs):
        """Create mock user for service testing"""
        mock_user = Mock()
        mock_user.id = kwargs.get('id', 1)
        mock_user.name = kwargs.get('name', 'Test User')
        mock_user.email = kwargs.get('email', '<EMAIL>')
        return mock_user
    
    def assert_service_success(self, result):
        """Assert service operation was successful"""
        self.assertIsNotNone(result)
        if isinstance(result, dict) and 'success' in result:
            self.assertTrue(result['success'])
    
    def assert_service_error(self, exception_class, callable_func, *args, **kwargs):
        """Assert service operation raises expected exception"""
        with self.assertRaises(exception_class):
            callable_func(*args, **kwargs)


class PerformanceTestCase(BaseTestCase):
    """
    Base test case for performance testing.
    """
    
    def setUp(self):
        """Set up performance test environment"""
        super().setUp()
        self.performance_data = {}
    
    def measure_time(self, operation_name):
        """Context manager to measure execution time"""
        import time
        from contextlib import contextmanager
        
        @contextmanager
        def timer():
            start = time.time()
            yield
            end = time.time()
            self.performance_data[operation_name] = end - start
        
        return timer()
    
    def assert_max_queries(self, max_queries):
        """Context manager to assert maximum number of database queries"""
        return self.assertNumQueries(max_queries)
    
    def assert_performance_threshold(self, operation_name, max_seconds):
        """Assert operation completed within time threshold"""
        actual_time = self.performance_data.get(operation_name)
        self.assertIsNotNone(actual_time, f"No timing data for operation: {operation_name}")
        self.assertLessEqual(
            actual_time, 
            max_seconds, 
            f"Operation {operation_name} took {actual_time:.3f}s, expected <= {max_seconds}s"
        )


class IntegrationTestCase(TransactionTestCase):
    """
    Base test case for integration testing with transaction support.
    """
    
    def setUp(self):
        """Set up integration test environment"""
        super().setUp()
        self.clear_cache()
        self.setup_test_data()
    
    def tearDown(self):
        """Clean up after integration tests"""
        super().tearDown()
        self.clear_cache()
    
    def clear_cache(self):
        """Clear all cache data"""
        cache.clear()
    
    def setup_test_data(self):
        """Set up test data for integration tests"""
        pass
    
    def run_in_transaction(self, func, *args, **kwargs):
        """Run function in database transaction"""
        with transaction.atomic():
            return func(*args, **kwargs)
    
    def assert_database_state(self, model_class, expected_count, **filters):
        """Assert database state matches expectations"""
        actual_count = model_class.objects.filter(**filters).count()
        self.assertEqual(
            actual_count, 
            expected_count,
            f"Expected {expected_count} {model_class.__name__} objects, found {actual_count}"
        )
