import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import CampaignCard from '../campaigns/CampaignCard';

const VirtualizedCampaignGrid = ({ 
  campaigns, 
  onCampaignClick, 
  onDeleteCampaign,
  itemsPerRow = 3,
  itemHeight = 280,
  containerHeight = 600,
  overscan = 5 
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerSize, setContainerSize] = useState({ width: 0, height: containerHeight });
  const containerRef = useRef(null);
  const scrollElementRef = useRef(null);

  // Calculate grid dimensions
  const { visibleItems, totalHeight, startIndex, endIndex } = useMemo(() => {
    if (!campaigns.length) {
      return { visibleItems: [], totalHeight: 0, startIndex: 0, endIndex: 0 };
    }

    const totalRows = Math.ceil(campaigns.length / itemsPerRow);
    const totalHeight = totalRows * itemHeight;
    
    // Calculate visible range
    const visibleStartRow = Math.floor(scrollTop / itemHeight);
    const visibleEndRow = Math.min(
      totalRows - 1,
      Math.ceil((scrollTop + containerSize.height) / itemHeight)
    );
    
    // Add overscan
    const startRow = Math.max(0, visibleStartRow - overscan);
    const endRow = Math.min(totalRows - 1, visibleEndRow + overscan);
    
    const startIndex = startRow * itemsPerRow;
    const endIndex = Math.min(campaigns.length - 1, (endRow + 1) * itemsPerRow - 1);
    
    const visibleItems = [];
    for (let i = startIndex; i <= endIndex; i++) {
      if (campaigns[i]) {
        const row = Math.floor(i / itemsPerRow);
        const col = i % itemsPerRow;
        visibleItems.push({
          campaign: campaigns[i],
          index: i,
          row,
          col,
          top: row * itemHeight,
          left: col * (100 / itemsPerRow) + '%',
          width: (100 / itemsPerRow) + '%'
        });
      }
    }
    
    return { visibleItems, totalHeight, startIndex, endIndex };
  }, [campaigns, scrollTop, containerSize, itemsPerRow, itemHeight, overscan]);

  // Handle scroll
  const handleScroll = useCallback((e) => {
    const newScrollTop = e.target.scrollTop;
    setScrollTop(newScrollTop);
  }, []);

  // Handle container resize
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerSize({ width: rect.width, height: rect.height });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  // Scroll to top when campaigns change
  useEffect(() => {
    if (scrollElementRef.current) {
      scrollElementRef.current.scrollTop = 0;
      setScrollTop(0);
    }
  }, [campaigns]);

  if (!campaigns.length) {
    return (
      <div className="flex items-center justify-center h-64 text-warmGray-500">
        <p>No campaigns found</p>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="relative w-full"
      style={{ height: containerHeight }}
    >
      <div
        ref={scrollElementRef}
        className="w-full h-full overflow-auto"
        onScroll={handleScroll}
      >
        {/* Virtual container with total height */}
        <div 
          className="relative w-full"
          style={{ height: totalHeight }}
        >
          {/* Render only visible items */}
          {visibleItems.map(({ campaign, index, top, left, width }) => (
            <div
              key={campaign.id || index}
              className="absolute px-3 pb-6"
              style={{
                top: `${top}px`,
                left: left,
                width: width,
                height: `${itemHeight}px`
              }}
            >
              <CampaignCard
                campaign={campaign}
                onClick={onCampaignClick}
                onDelete={onDeleteCampaign}
              />
            </div>
          ))}
        </div>
      </div>
      
      {/* Debug info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs p-2 rounded">
          <div>Total: {campaigns.length}</div>
          <div>Visible: {startIndex}-{endIndex}</div>
          <div>Rendered: {visibleItems.length}</div>
        </div>
      )}
    </div>
  );
};

export default React.memo(VirtualizedCampaignGrid);
